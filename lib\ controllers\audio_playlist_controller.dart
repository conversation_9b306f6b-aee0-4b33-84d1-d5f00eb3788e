import 'package:get/get.dart';
import 'package:hive/hive.dart';
import '../model/audio_playlist_model.dart';

class AudioPlaylistController extends GetxController {
  final Box<AudioPlaylist> audioPlaylistBox =
      Hive.box<AudioPlaylist>('audioPlaylists');
  RxList<AudioPlaylist> playlists = <AudioPlaylist>[].obs;

  @override
  void onInit() {
    super.onInit();
    playlists.assignAll(audioPlaylistBox.values);
    audioPlaylistBox.watch().listen((event) {
      playlists.assignAll(audioPlaylistBox.values);
    });
  }

  // ✅ إضافة قائمة جديدة (مع التحقق من التكرار)
  bool addPlaylist(String name) {
    if (playlists.any((p) => p.name == name)) return false;
    audioPlaylistBox.add(AudioPlaylist(name: name, songIds: []));
    return true;
  }

  // ✅ حذف قائمة
  void deletePlaylist(int index) {
    audioPlaylistBox.getAt(index)?.delete();
  }

  // ✅ إعادة تسمية قائمة
  void renamePlaylist(int index, String newName) {
    final playlist = audioPlaylistBox.getAt(index);
    if (playlist != null) {
      playlist.name = newName;
      playlist.save();
    }
  }

  // ✅ إضافة أغنية لقائمة
  void addSongToPlaylist(int playlistIndex, int songId) {
    final playlist = audioPlaylistBox.getAt(playlistIndex);
    if (playlist != null && !playlist.songIds.contains(songId)) {
      playlist.songIds.add(songId);
      playlist.save();
      update();
    }
  }

  // ✅ إزالة أغنية من قائمة
  void removeSongFromPlaylist(int playlistIndex, int songId) {
    final playlist = audioPlaylistBox.getAt(playlistIndex);
    if (playlist != null && playlist.songIds.contains(songId)) {
      playlist.songIds.remove(songId);
      playlist.save();
    }
  }

  // ✅ تشغيل كل الأغاني داخل قائمة تشغيل (يرجى ربطها بكود التشغيل في AudioController)
  List<int> getSongsFromPlaylist(int playlistIndex) {
    final playlist = audioPlaylistBox.getAt(playlistIndex);
    return playlist?.songIds ?? [];
  }

  // ✅ نقل أغنية من قائمة إلى أخرى
  void moveSongToAnotherPlaylist({
    required int fromIndex,
    required int toIndex,
    required int songId,
  }) {
    final fromPlaylist = audioPlaylistBox.getAt(fromIndex);
    final toPlaylist = audioPlaylistBox.getAt(toIndex);

    if (fromPlaylist != null &&
        toPlaylist != null &&
        fromPlaylist.songIds.contains(songId)) {
      fromPlaylist.songIds.remove(songId);
      toPlaylist.songIds.add(songId);
      fromPlaylist.save();
      toPlaylist.save();
    }
  }

  void reorderSongsInPlaylist(int index, int oldIndex, int newIndex) {
    final playlist = audioPlaylistBox.getAt(index);
    if (playlist != null) {
      if (newIndex > oldIndex) newIndex -= 1;
      final songId = playlist.songIds.removeAt(oldIndex);
      playlist.songIds.insert(newIndex, songId);
      playlist.save();
    }
  }

  // ✅ إعادة ترتيب الأغاني داخل قائمة

  /// ✅ هذا هو الإجراء الإضافي المستخدم في صفحة الصوتيات
  void addAudioToPlaylist(int index, int songId) {
    addSongToPlaylist(index, songId);
  }
}
