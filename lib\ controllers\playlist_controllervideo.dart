import 'package:get/get.dart';
import 'package:hive/hive.dart';

import '../model/playlist_model.dart';

class PlaylistController extends GetxController {
  final Box<Playlist> playlistBox = Hive.box<Playlist>('playlists');
  RxList<Playlist> playlists = <Playlist>[].obs;

  @override
  void onInit() {
    super.onInit();
    playlists.assignAll(playlistBox.values);
    playlistBox.watch().listen((event) {
      playlists.assignAll(playlistBox.values);
    });
  }

  void addPlaylist(String name) {
    playlistBox.add(Playlist(name: name, videoPaths: []));
  }

  void deletePlaylist(int index) {
    playlistBox.getAt(index)?.delete();
  }

  void renamePlaylist(int index, String newName) {
    final playlist = playlistBox.getAt(index);
    if (playlist != null) {
      playlist.name = newName;
      playlist.save();
    }
  }

  void addVideoToPlaylist(int playlistIndex, String videoPath) {
    final playlist = playlistBox.getAt(playlistIndex);
    if (playlist != null && !playlist.videoPaths.contains(videoPath)) {
      playlist.videoPaths.add(videoPath);
      playlist.save();
    }
  }

  void removeVideoFromPlaylist(int playlistIndex, String videoPath) {
    final playlist = playlistBox.getAt(playlistIndex);
    if (playlist != null && playlist.videoPaths.contains(videoPath)) {
      playlist.videoPaths.remove(videoPath);
      playlist.save();
    }
  }
}