import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';

import ' controllers/audio_controller.dart';
import ' controllers/audio_playlist_controller.dart';
import ' controllers/media_controller.dart';
import ' controllers/playlist_controllervideo.dart';
import 'model/audio_playlist_model.dart';
import 'model/playlist_model.dart';
import 'pages/main_navigation_page.dart';
import ' controllers/theme_controller.dart';
import ' controllers/video_player_controller.dart';
import 'services/simple_audio_player_service.dart';
import 'services/database_service.dart';
import 'themes/app_themes.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة Hive
  await Hive.initFlutter();
  Hive.registerAdapter(AudioPlaylistAdapter());
  Hive.registerAdapter(PlaylistAdapter());
  await Hive.openBox<Playlist>('playlists');
  await Hive.openBox<AudioPlaylist>('audioPlaylists');
  await _initPermissions(); // انتظر الصلاحيات قبل المتابعة

  runApp(const MyApp());
}

Future<void> _initPermissions() async {
  try {
    if (Platform.isAndroid) {
      // التحقق من إصدار Android أولاً
      final androidInfo = await DeviceInfoPlugin().androidInfo;

      if (androidInfo.version.sdkInt >= 33) {
        // Android 13+ permissions - طلب صلاحيات محددة
        final permissions = [
          Permission.audio,
          Permission.videos,
          Permission.photos,
        ];

        for (final permission in permissions) {
          try {
            final status = await permission.status;
            if (status.isDenied || status.isPermanentlyDenied) {
              final result = await permission.request();
              if (result.isPermanentlyDenied) {
                // يمكن إظهار رسالة للمستخدم لفتح الإعدادات
                print('تم رفض الصلاحية نهائياً: $permission');
              }
            }
          } catch (e) {
            print('خطأ في طلب صلاحية $permission: $e');
            // المتابعة مع الصلاحية التالية - لا نوقف التطبيق
            continue;
          }
        }
      } else {
        // Android 12 وأقل - استخدام صلاحية التخزين التقليدية
        try {
          final storageStatus = await Permission.storage.status;
          if (storageStatus.isDenied) {
            await Permission.storage.request();
          }
        } catch (e) {
          print('خطأ في طلب صلاحية التخزين: $e');
        }
      }
    }
  } catch (e) {
    print('خطأ عام في تهيئة الصلاحيات: $e');
    // المتابعة بدون صلاحيات - التطبيق سيعمل بوظائف محدودة
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // إنشاء كنترولر الثيم أولاً
    final themeController = Get.put(ThemeController());

    return Obx(() => GetMaterialApp(
          title: 'Murad Player',
          debugShowCheckedModeBanner: false,
          // استخدام الثيم الديناميكي من الكنترولر
          theme: _getThemeFromController(themeController),
          home: const MainNavigationPage(),
          initialBinding: BindingsBuilder(() {
            Get.put(PlaylistController());
            Get.put(MediaController());
            Get.put(AudioController());
            Get.put(AudioPlaylistController());
            // Get.put(EnhancedAudioPlayerService());
            Get.put(SimpleAudioPlayerService());
            Get.put(DatabaseService());
            Get.put(VideoPlayerController());
          }),
        ));
  }

  /// دالة للحصول على الثيم من الكنترولر
  ThemeData _getThemeFromController(ThemeController controller) {
    switch (controller.currentTheme) {
      case 'light':
        return AppThemes.lightTheme;
      case 'purple_dark':
        return AppThemes.purpleTheme;
      case 'green_dark':
        return AppThemes.greenTheme;
      case 'orange_dark':
        return AppThemes.orangeTheme;
      case 'blue_dark':
        return AppThemes.blueTheme;
      case 'red_dark':
        return AppThemes.redTheme;
      default:
        return AppThemes.darkTheme;
    }
  }
}
