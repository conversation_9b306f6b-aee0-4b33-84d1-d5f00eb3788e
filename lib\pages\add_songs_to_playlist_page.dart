import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../ controllers/audio_controller.dart';
import '../ controllers/audio_playlist_controller.dart';

/// صفحة إضافة الأغاني لقائمة التشغيل - تصميم احترافي مثل Lark Player
class AddSongsToPlaylistPage extends StatefulWidget {
  final AudioPlaylistController controller;
  final int playlistIndex;

  const AddSongsToPlaylistPage({
    super.key,
    required this.controller,
    required this.playlistIndex,
  });

  @override
  State<AddSongsToPlaylistPage> createState() => _AddSongsToPlaylistPageState();
}

class _AddSongsToPlaylistPageState extends State<AddSongsToPlaylistPage> {
  final TextEditingController _searchController = TextEditingController();
  final RxString searchQuery = ''.obs;
  final RxBool isSearching = false.obs;
  final RxSet<int> selectedSongs = <int>{}.obs;
  final RxBool selectAll = false.obs;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      searchQuery.value = _searchController.text;
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final audioController = Get.find<AudioController>();
    final playlist = widget.controller.playlists[widget.playlistIndex];

    return Scaffold(
      backgroundColor: Get.theme.scaffoldBackgroundColor,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          // شريط البحث
          _buildSearchBar(),

          // شريط التحديد الكلي
          _buildSelectAllBar(),

          // قائمة الأغاني
          Expanded(
            child: Obx(() {
              final allSongs = audioController.allSongs;
              final availableSongs = allSongs
                  .where((song) => !playlist.songIds.contains(song.id))
                  .toList();

              // تطبيق البحث
              final filteredSongs = searchQuery.value.isEmpty
                  ? availableSongs
                  : availableSongs
                      .where((song) =>
                          song.title
                              .toLowerCase()
                              .contains(searchQuery.value.toLowerCase()) ||
                          (song.artist
                                  ?.toLowerCase()
                                  .contains(searchQuery.value.toLowerCase()) ??
                              false))
                      .toList();

              if (availableSongs.isEmpty) {
                return _buildEmptyState();
              }

              if (filteredSongs.isEmpty && searchQuery.value.isNotEmpty) {
                return _buildNoSearchResults();
              }

              return _buildSongsList(filteredSongs);
            }),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Get.theme.scaffoldBackgroundColor,
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color: Get.theme.colorScheme.onSurface,
        ),
        onPressed: () => Get.back(),
      ),
      title: Text(
        'إضافة أغاني',
        style: TextStyle(
          color: Get.theme.colorScheme.onSurface,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      actions: [
        Obx(() => IconButton(
              icon: Icon(
                isSearching.value ? Icons.close : Icons.search,
                color: Get.theme.colorScheme.onSurface,
              ),
              onPressed: () {
                if (isSearching.value) {
                  isSearching.value = false;
                  _searchController.clear();
                } else {
                  isSearching.value = true;
                }
              },
            )),
      ],
    );
  }

  /// بناء شريط البحث
  Widget _buildSearchBar() {
    return Obx(() => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          height: isSearching.value ? 60 : 0,
          child: isSearching.value
              ? Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: TextField(
                    controller: _searchController,
                    autofocus: true,
                    decoration: InputDecoration(
                      hintText: 'البحث في الأغاني...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Get.theme.colorScheme.surface,
                    ),
                  ),
                )
              : const SizedBox.shrink(),
        ));
  }

  /// بناء شريط التحديد الكلي
  Widget _buildSelectAllBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Get.theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Obx(() => Checkbox(
                value: selectAll.value,
                onChanged: (value) => _toggleSelectAll(),
              )),
          const SizedBox(width: 8),
          Text(
            'تحديد الكل',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Get.theme.colorScheme.onSurface,
            ),
          ),
          const Spacer(),
          Obx(() => Text(
                '${selectedSongs.length} محدد',
                style: TextStyle(
                  fontSize: 14,
                  color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              )),
        ],
      ),
    );
  }

  /// بناء قائمة الأغاني
  Widget _buildSongsList(List songs) {
    return ListView.builder(
      itemCount: songs.length,
      itemBuilder: (context, index) {
        final song = songs[index];
        return Obx(() => Card(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: CheckboxListTile(
                value: selectedSongs.contains(song.id),
                onChanged: (selected) => _toggleSongSelection(song.id),
                secondary: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Get.theme.colorScheme.primary,
                        Get.theme.colorScheme.secondary,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.music_note,
                    color: Get.theme.colorScheme.onPrimary,
                  ),
                ),
                title: Text(
                  song.title,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Get.theme.colorScheme.onSurface,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      song.artist ?? "غير معروف",
                      style: TextStyle(
                        color: Get.theme.colorScheme.onSurface
                            .withValues(alpha: 0.7),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (song.duration != null)
                      Text(
                        _formatDuration(Duration(milliseconds: song.duration!)),
                        style: TextStyle(
                          fontSize: 12,
                          color: Get.theme.colorScheme.onSurface
                              .withValues(alpha: 0.5),
                        ),
                      ),
                  ],
                ),
                controlAffinity: ListTileControlAffinity.leading,
              ),
            ));
      },
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Get.theme.colorScheme.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.music_off,
              size: 64,
              color: Get.theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'جميع الأغاني موجودة في القائمة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Get.theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لا توجد أغاني جديدة لإضافتها',
            style: TextStyle(
              fontSize: 14,
              color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة عدم وجود نتائج بحث
  Widget _buildNoSearchResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد نتائج للبحث',
            style: TextStyle(
              fontSize: 16,
              color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء الشريط السفلي
  Widget _buildBottomBar() {
    return Obx(() => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          height: selectedSongs.isEmpty ? 0 : 80,
          child: selectedSongs.isEmpty
              ? const SizedBox.shrink()
              : Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Get.theme.cardColor,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, -2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      // معلومات التحديد
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '${selectedSongs.length} أغنية محددة',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Get.theme.colorScheme.onSurface,
                              ),
                            ),
                            Text(
                              'جاهزة للإضافة',
                              style: TextStyle(
                                fontSize: 12,
                                color: Get.theme.colorScheme.onSurface
                                    .withValues(alpha: 0.7),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // زر الإلغاء
                      TextButton(
                        onPressed: () => _clearSelection(),
                        child: const Text('إلغاء'),
                      ),

                      const SizedBox(width: 8),

                      // زر الإضافة
                      ElevatedButton.icon(
                        onPressed: () => _addSelectedSongs(),
                        icon: const Icon(Icons.add),
                        label: Text('إضافة (${selectedSongs.length})'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Get.theme.colorScheme.primary,
                          foregroundColor: Get.theme.colorScheme.onPrimary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                        ),
                      ),
                    ],
                  ),
                ),
        ));
  }

  /// تبديل تحديد أغنية
  void _toggleSongSelection(int songId) {
    if (selectedSongs.contains(songId)) {
      selectedSongs.remove(songId);
    } else {
      selectedSongs.add(songId);
    }
    _updateSelectAllState();
  }

  /// تبديل تحديد الكل
  void _toggleSelectAll() {
    final audioController = Get.find<AudioController>();
    final playlist = widget.controller.playlists[widget.playlistIndex];
    final availableSongs = audioController.allSongs
        .where((song) => !playlist.songIds.contains(song.id))
        .toList();

    // تطبيق البحث
    final filteredSongs = searchQuery.value.isEmpty
        ? availableSongs
        : availableSongs
            .where((song) =>
                song.title
                    .toLowerCase()
                    .contains(searchQuery.value.toLowerCase()) ||
                (song.artist
                        ?.toLowerCase()
                        .contains(searchQuery.value.toLowerCase()) ??
                    false))
            .toList();

    if (selectAll.value) {
      // إلغاء تحديد الكل
      for (final song in filteredSongs) {
        selectedSongs.remove(song.id);
      }
    } else {
      // تحديد الكل
      for (final song in filteredSongs) {
        selectedSongs.add(song.id);
      }
    }
    _updateSelectAllState();
  }

  /// تحديث حالة تحديد الكل
  void _updateSelectAllState() {
    final audioController = Get.find<AudioController>();
    final playlist = widget.controller.playlists[widget.playlistIndex];
    final availableSongs = audioController.allSongs
        .where((song) => !playlist.songIds.contains(song.id))
        .toList();

    // تطبيق البحث
    final filteredSongs = searchQuery.value.isEmpty
        ? availableSongs
        : availableSongs
            .where((song) =>
                song.title
                    .toLowerCase()
                    .contains(searchQuery.value.toLowerCase()) ||
                (song.artist
                        ?.toLowerCase()
                        .contains(searchQuery.value.toLowerCase()) ??
                    false))
            .toList();

    if (filteredSongs.isEmpty) {
      selectAll.value = false;
      return;
    }

    final allSelected =
        filteredSongs.every((song) => selectedSongs.contains(song.id));
    selectAll.value = allSelected;
  }

  /// مسح التحديد
  void _clearSelection() {
    selectedSongs.clear();
    selectAll.value = false;
  }

  /// إضافة الأغاني المحددة
  void _addSelectedSongs() {
    if (selectedSongs.isEmpty) return;

    // إضافة الأغاني للقائمة
    for (final songId in selectedSongs) {
      widget.controller.addSongToPlaylist(widget.playlistIndex, songId);
    }

    // عرض رسالة نجاح
    Get.snackbar(
      'تم بنجاح',
      'تم إضافة ${selectedSongs.length} أغنية إلى القائمة',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Get.theme.colorScheme.primary,
      colorText: Get.theme.colorScheme.onPrimary,
      duration: const Duration(seconds: 2),
      margin: const EdgeInsets.all(16),
      borderRadius: 12,
    );

    // العودة للصفحة السابقة
    Get.back();
  }

  /// تنسيق مدة الأغنية
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return "${duration.inHours > 0 ? '${twoDigits(duration.inHours)}:' : ''}$twoDigitMinutes:$twoDigitSeconds";
  }
}
