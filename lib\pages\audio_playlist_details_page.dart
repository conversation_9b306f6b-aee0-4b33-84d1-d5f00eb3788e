import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../ controllers/audio_controller.dart';
import '../ controllers/audio_playlist_controller.dart';
import '../model/media_item.dart';
import '../services/enhanced_audio_player_service.dart';

/// صفحة تفاصيل قائمة التشغيل الصوتية
/// تعرض جميع الأغاني في القائمة مع إمكانية البحث والإدارة
class AudioPlaylistDetailsPage extends StatefulWidget {
  final int playlistIndex;
  final String playlistName;

  const AudioPlaylistDetailsPage({
    super.key,
    required this.playlistIndex,
    required this.playlistName,
  });

  @override
  State<AudioPlaylistDetailsPage> createState() =>
      _AudioPlaylistDetailsPageState();
}

class _AudioPlaylistDetailsPageState extends State<AudioPlaylistDetailsPage> {
  final TextEditingController _searchController = TextEditingController();
  final RxString searchQuery = ''.obs;
  final RxBool isSearching = false.obs;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      searchQuery.value = _searchController.text;
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final audioPlaylistController = Get.find<AudioPlaylistController>();
    final audioController = Get.find<AudioController>();

    return Scaffold(
      backgroundColor: Get.theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Get.theme.colorScheme.primary,
                Get.theme.colorScheme.secondary,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        title: Obx(() => isSearching.value
            ? TextField(
                controller: _searchController,
                autofocus: true,
                style: TextStyle(color: Get.theme.colorScheme.onPrimary),
                decoration: InputDecoration(
                  hintText: 'البحث في الأغاني...',
                  hintStyle: TextStyle(
                    color:
                        Get.theme.colorScheme.onPrimary.withValues(alpha: 0.7),
                  ),
                  border: InputBorder.none,
                ),
              )
            : Text(
                widget.playlistName,
                style: TextStyle(
                  color: Get.theme.colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              )),
        actions: [
          Obx(() => IconButton(
                icon: Icon(
                  isSearching.value ? Icons.close : Icons.search,
                  color: Get.theme.colorScheme.onPrimary,
                ),
                onPressed: () {
                  if (isSearching.value) {
                    isSearching.value = false;
                    _searchController.clear();
                  } else {
                    isSearching.value = true;
                  }
                },
              )),
          PopupMenuButton<String>(
            icon: Icon(
              Icons.more_vert,
              color: Get.theme.colorScheme.onPrimary,
            ),
            onSelected: (value) =>
                _handleMenuAction(value, audioPlaylistController),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'add_songs',
                child: Row(
                  children: [
                    Icon(Icons.library_add),
                    SizedBox(width: 8),
                    Text('إضافة أغاني'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'play_all',
                child: Row(
                  children: [
                    Icon(Icons.play_arrow),
                    SizedBox(width: 8),
                    Text('تشغيل الكل'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'shuffle',
                child: Row(
                  children: [
                    Icon(Icons.shuffle),
                    SizedBox(width: 8),
                    Text('تشغيل عشوائي'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Obx(() {
        final playlist =
            audioPlaylistController.playlists[widget.playlistIndex];
        final allSongs = audioController.allSongs;
        final playlistSongs = allSongs
            .where((song) => playlist.songIds.contains(song.id))
            .toList();

        // تطبيق البحث
        final filteredSongs = searchQuery.value.isEmpty
            ? playlistSongs
            : playlistSongs
                .where((song) =>
                    song.title
                        .toLowerCase()
                        .contains(searchQuery.value.toLowerCase()) ||
                    (song.artist
                            ?.toLowerCase()
                            .contains(searchQuery.value.toLowerCase()) ??
                        false))
                .toList();

        if (playlistSongs.isEmpty) {
          return _buildEmptyState(audioPlaylistController);
        }

        return Column(
          children: [
            // معلومات القائمة
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Get.theme.colorScheme.primary,
                          Get.theme.colorScheme.secondary,
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.music_note,
                      size: 40,
                      color: Get.theme.colorScheme.onPrimary,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.playlistName,
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Get.theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${playlistSongs.length} أغنية',
                          style: TextStyle(
                            fontSize: 14,
                            color: Get.theme.colorScheme.onSurface
                                .withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (playlistSongs.isNotEmpty)
                    ElevatedButton.icon(
                      onPressed: () => _playAllSongs(playlistSongs),
                      icon: const Icon(Icons.play_arrow),
                      label: const Text('تشغيل'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Get.theme.colorScheme.primary,
                        foregroundColor: Get.theme.colorScheme.onPrimary,
                      ),
                    ),
                ],
              ),
            ),

            const Divider(),

            // قائمة الأغاني
            Expanded(
              child: filteredSongs.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.search_off,
                            size: 64,
                            color: Get.theme.colorScheme.onSurface
                                .withValues(alpha: 0.5),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'لا توجد نتائج للبحث',
                            style: TextStyle(
                              fontSize: 16,
                              color: Get.theme.colorScheme.onSurface
                                  .withValues(alpha: 0.7),
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: filteredSongs.length,
                      itemBuilder: (context, index) {
                        final song = filteredSongs[index];
                        return _buildSongTile(
                            song, index, audioPlaylistController);
                      },
                    ),
            ),
          ],
        );
      }),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState(AudioPlaylistController controller) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Get.theme.colorScheme.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.music_off,
                size: 64,
                color: Get.theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'قائمة فارغة',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Get.theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'لا توجد أغاني في هذه القائمة بعد',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () => _showAddSongsDialog(controller),
              icon: const Icon(Icons.add),
              label: const Text('إضافة أغاني'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Get.theme.colorScheme.primary,
                foregroundColor: Get.theme.colorScheme.onPrimary,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر الأغنية
  Widget _buildSongTile(song, int index, AudioPlaylistController controller) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: Get.theme.colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.music_note,
            color: Get.theme.colorScheme.primary,
          ),
        ),
        title: Text(
          song.title,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: Get.theme.colorScheme.onSurface,
          ),
        ),
        subtitle: Text(
          song.artist ?? "غير معروف",
          style: TextStyle(
            color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
        trailing: PopupMenuButton<String>(
          icon: Icon(
            Icons.more_vert,
            color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          onSelected: (value) => _handleSongAction(value, song, controller),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'play',
              child: Row(
                children: [
                  Icon(Icons.play_arrow),
                  SizedBox(width: 8),
                  Text('تشغيل'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'remove',
              child: Row(
                children: [
                  Icon(Icons.remove_circle, color: Colors.red),
                  SizedBox(width: 8),
                  Text('إزالة من القائمة', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        onTap: () => _playSong(song, index),
      ),
    );
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(String action, AudioPlaylistController controller) {
    switch (action) {
      case 'add_songs':
        _showAddSongsDialog(controller);
        break;
      case 'play_all':
        final playlist = controller.playlists[widget.playlistIndex];
        final audioController = Get.find<AudioController>();
        final songs = audioController.allSongs
            .where((song) => playlist.songIds.contains(song.id))
            .toList();
        if (songs.isNotEmpty) {
          _playAllSongs(songs);
        }
        break;
      case 'shuffle':
        final playlist = controller.playlists[widget.playlistIndex];
        final audioController = Get.find<AudioController>();
        final songs = audioController.allSongs
            .where((song) => playlist.songIds.contains(song.id))
            .toList();
        if (songs.isNotEmpty) {
          songs.shuffle();
          _playAllSongs(songs);
        }
        break;
    }
  }

  /// معالجة إجراءات الأغنية
  void _handleSongAction(
      String action, song, AudioPlaylistController controller) {
    switch (action) {
      case 'play':
        _playSong(song, 0);
        break;
      case 'remove':
        _showRemoveConfirmation(song, controller);
        break;
    }
  }

  /// تشغيل أغنية واحدة
  void _playSong(song, int index) {
    final mediaItem = MediaItem(
      id: song.id.toString(),
      title: song.title,
      path: song.data,
      artist: song.artist,
      album: song.album,
      duration: Duration(milliseconds: song.duration ?? 0),
      type: MediaType.audio,
      size: song.size,
      dateAdded: DateTime.fromMillisecondsSinceEpoch(song.dateAdded ?? 0),
    );

    final playerService = EnhancedAudioPlayerService.instance;
    playerService.playMediaItem(mediaItem, playlistName: widget.playlistName);
    playerService.setPlayerSize(fullScreen: true);
  }

  /// تشغيل جميع الأغاني
  void _playAllSongs(List songs) {
    if (songs.isEmpty) return;

    final playlistItems = songs
        .map((s) => MediaItem(
              id: s.id.toString(),
              title: s.title,
              path: s.data,
              artist: s.artist,
              album: s.album,
              duration: Duration(milliseconds: s.duration ?? 0),
              type: MediaType.audio,
              size: s.size,
              dateAdded: DateTime.fromMillisecondsSinceEpoch(s.dateAdded ?? 0),
            ))
        .toList();

    final playerService = EnhancedAudioPlayerService.instance;
    playerService.playMediaItem(
      playlistItems.first,
      playlist: playlistItems,
      playlistName: widget.playlistName,
    );
    playerService.setPlayerSize(fullScreen: true);
  }

  /// عرض حوار إضافة أغاني
  void _showAddSongsDialog(AudioPlaylistController controller) {
    Get.snackbar('قريباً', 'ميزة إضافة الأغاني ستكون متاحة قريباً');
  }

  /// عرض تأكيد إزالة الأغنية
  void _showRemoveConfirmation(song, AudioPlaylistController controller) {
    Get.dialog(
      AlertDialog(
        backgroundColor: Get.theme.dialogBackgroundColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text(
          'إزالة الأغنية',
          style: TextStyle(color: Get.theme.colorScheme.onSurface),
        ),
        content: Text(
          'هل تريد إزالة "${song.title}" من هذه القائمة؟',
          style: TextStyle(color: Get.theme.colorScheme.onSurface),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              controller.removeSongFromPlaylist(widget.playlistIndex, song.id);
              Get.back();
              Get.snackbar('تم', 'تم إزالة الأغنية من القائمة');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('إزالة'),
          ),
        ],
      ),
    );
  }
}
