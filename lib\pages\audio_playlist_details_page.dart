import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:social_media_app/%20controllers/audio_controller.dart';
import 'package:social_media_app/%20controllers/audio_playlist_controller.dart';
import 'package:social_media_app/%20controllers/media_controller.dart';
import 'package:social_media_app/services/simple_audio_player_service.dart';

import '../model/media_item.dart';
import 'add_songs_to_playlist_page.dart';

/// صفحة تفاصيل قائمة التشغيل الصوتية – نسخة مصغّرة ومصحّحة
class AudioPlaylistDetailsPage extends StatefulWidget {
  final int playlistIndex;
  final String playlistName;

  const AudioPlaylistDetailsPage({
    super.key,
    required this.playlistIndex,
    required this.playlistName,
  });

  @override
  State<AudioPlaylistDetailsPage> createState() =>
      _AudioPlaylistDetailsPageState();
}

class _AudioPlaylistDetailsPageState extends State<AudioPlaylistDetailsPage> {
  final TextEditingController _searchController = TextEditingController();
  final RxString searchQuery = ''.obs;
  final RxBool isSearching = false.obs;

  @override
  void initState() {
    super.initState();
    _searchController
        .addListener(() => searchQuery.value = _searchController.text);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final audioPlaylistController = Get.find<AudioPlaylistController>();
    final audioController = Get.find<AudioController>();
    final mediaController = Get.find<MediaController>();

    return Scaffold(
      backgroundColor: Get.theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Get.theme.colorScheme.primary,
                Get.theme.colorScheme.secondary,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        title: Obx(() => isSearching.value
            ? TextField(
                controller: _searchController,
                autofocus: true,
                style: TextStyle(color: Get.theme.colorScheme.onPrimary),
                decoration: InputDecoration(
                  hintText: 'البحث في الأغاني...',
                  hintStyle: TextStyle(
                    color:
                        Get.theme.colorScheme.onPrimary.withValues(alpha: 0.7),
                  ),
                  border: InputBorder.none,
                ),
              )
            : Text(
                widget.playlistName,
                style: TextStyle(
                  color: Get.theme.colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              )),
        actions: [
          Obx(() => IconButton(
                icon: Icon(
                  isSearching.value ? Icons.close : Icons.search,
                  color: Get.theme.colorScheme.onPrimary,
                ),
                onPressed: () {
                  if (isSearching.value) {
                    isSearching.value = false;
                    _searchController.clear();
                  } else {
                    isSearching.value = true;
                  }
                },
              )),
          PopupMenuButton<String>(
            icon: Icon(Icons.more_vert, color: Get.theme.colorScheme.onPrimary),
            onSelected: (value) =>
                _handleMenuAction(value, audioPlaylistController),
            itemBuilder: (_) => [
              const PopupMenuItem(
                value: 'add_songs',
                child: Row(children: [
                  Icon(Icons.library_add),
                  SizedBox(width: 8),
                  Text('إضافة أغاني')
                ]),
              ),
              const PopupMenuItem(
                value: 'play_all',
                child: Row(children: [
                  Icon(Icons.play_arrow),
                  SizedBox(width: 8),
                  Text('تشغيل الكل')
                ]),
              ),
              const PopupMenuItem(
                value: 'shuffle',
                child: Row(children: [
                  Icon(Icons.shuffle),
                  SizedBox(width: 8),
                  Text('تشغيل عشوائي')
                ]),
              ),
            ],
          ),
        ],
      ),
      body: Obx(() {
        final playlist =
            audioPlaylistController.playlists[widget.playlistIndex];
        print("allSongs-------------------------");
        print("${playlist.songIds.runtimeType}");
        final allSongs = mediaController.allAudioFiles;
        print("${allSongs.length}");

        final playlistSongs = allSongs
            .where((s) => playlist.songIds.contains(int.parse(s.id)))
            .toList();
        print("playlistSongs----------------${playlistSongs}---------");

        print("Playlist Songs Count: ${playlistSongs.length}");
        print(
            "All Songs IDs: ${mediaController.allAudioFiles.map((e) => e.id)}");
        print("Playlist IDs: ${playlist.songIds}");
        print("${playlistSongs.length}");
        final filtered = searchQuery.value.isEmpty
            ? playlistSongs
            : playlistSongs
                .where((s) =>
                    s.title
                        .toLowerCase()
                        .contains(searchQuery.value.toLowerCase()) ||
                    (s.artist
                            ?.toLowerCase()
                            .contains(searchQuery.value.toLowerCase()) ??
                        false))
                .toList();

        // 1) القائمة فارغة
        if (playlistSongs.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.music_off,
                    size: 64, color: Get.theme.colorScheme.primary),
                const SizedBox(height: 20),
                const Text('قائمة فارغة',
                    style:
                        TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                const Text('لا توجد أغاني في هذه القائمة بعد'),
                const SizedBox(height: 30),
                ElevatedButton.icon(
                  onPressed: () => _showAddSongsDialog(audioPlaylistController),
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة أغاني'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Get.theme.colorScheme.primary,
                    foregroundColor: Get.theme.colorScheme.onPrimary,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12)),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                  ),
                ),
              ],
            ),
          );
        }

        // 2) فيها أغاني
        return Column(
          children: [
            // معلومات القائمة
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Get.theme.colorScheme.primary,
                          Get.theme.colorScheme.secondary
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(Icons.music_note,
                        size: 40, color: Get.theme.colorScheme.onPrimary),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(widget.playlistName,
                            style: const TextStyle(
                                fontSize: 20, fontWeight: FontWeight.bold)),
                        Text('${playlistSongs.length} أغنية',
                            style: TextStyle(
                                color: Get.theme.colorScheme.onSurface
                                    .withValues(alpha: 0.7))),
                      ],
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: () => _playAllSongs(playlistSongs),
                    icon: const Icon(Icons.play_arrow),
                    label: const Text('تشغيل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Get.theme.colorScheme.primary,
                      foregroundColor: Get.theme.colorScheme.onPrimary,
                    ),
                  ),
                ],
              ),
            ),
            const Divider(),
            // قائمة الأغاني
            Expanded(
              child: filtered.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.search_off,
                              size: 64,
                              color: Get.theme.colorScheme.onSurface
                                  .withValues(alpha: 0.5)),
                          const SizedBox(height: 16),
                          const Text('لا توجد نتائج للبحث'),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: filtered.length,
                      itemBuilder: (_, index) => _buildSongTile(
                          filtered[index], audioPlaylistController),
                    ),
            ),
          ],
        );
      }),
    );
  }

  /* ---------- العناصر المساعدة ---------- */
  Widget _buildSongTile(song, AudioPlaylistController controller) => Card(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        child: ListTile(
          leading: Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Get.theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(Icons.music_note, color: Get.theme.colorScheme.primary),
          ),
          title: Text(song.title,
              style: const TextStyle(fontWeight: FontWeight.w600)),
          subtitle: Text(song.artist ?? "غير معروف"),
          trailing: PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (v) => _handleSongAction(v, song, controller),
            itemBuilder: (_) => [
              const PopupMenuItem(
                  value: 'play',
                  child: Row(children: [
                    Icon(Icons.play_arrow),
                    SizedBox(width: 8),
                    Text('تشغيل')
                  ])),
              const PopupMenuItem(
                  value: 'remove',
                  child: Row(children: [
                    Icon(Icons.remove_circle, color: Colors.red),
                    SizedBox(width: 8),
                    Text('إزالة من القائمة',
                        style: TextStyle(color: Colors.red))
                  ])),
            ],
          ),
          onTap: () => _playSong(song, 0),
        ),
      );

  /* ---------- الأحداث ---------- */
  void _handleMenuAction(String action, AudioPlaylistController controller) {
    final playlist = controller.playlists[widget.playlistIndex];
    final audioController = Get.find<AudioController>();
    final songs = audioController.allSongs
        .where((s) => playlist.songIds.contains(s.id))
        .toList();

    switch (action) {
      case 'add_songs':
        _showAddSongsDialog(controller);
        break;
      case 'play_all':
        if (songs.isNotEmpty) _playAllSongs(songs);
        break;
      case 'shuffle':
        if (songs.isNotEmpty) {
          songs.shuffle();
          _playAllSongs(songs);
        }
        break;
    }
  }

  void _handleSongAction(
      String action, song, AudioPlaylistController controller) {
    switch (action) {
      case 'play':
        _playSong(song, 0);
        break;
      case 'remove':
        Get.dialog(
          AlertDialog(
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            title: const Text('إزالة الأغنية'),
            content: Text('هل تريد إزالة "${song.title}" من القائمة؟'),
            actions: [
              TextButton(onPressed: Get.back, child: const Text('إلغاء')),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red, foregroundColor: Colors.white),
                onPressed: () {
                  controller.removeSongFromPlaylist(
                      widget.playlistIndex, song.id);
                  Get.back();
                  Get.snackbar('تم', 'تم إزالة الأغنية من القائمة');
                },
                child: const Text('إزالة'),
              ),
            ],
          ),
        );
        break;
    }
  }

  void _playSong(song, int index) {
    final mediaItem = MediaItem(
      id: song.id.toString(),
      title: song.title,
      path: song.path, // تأكد من مصدر المسار الصحيح
      artist: song.artist,
      album: song.album,
      // duration: song.duration != null
      //     ? Duration(milliseconds: int.tryParse(song.duration) ?? 0)
      //     : null,
      type: MediaType.audio,
      size: song.size,
      // dateAdded: song.dateAdded != null
      //     ? DateTime.fromMillisecondsSinceEpoch(song.dateAdded)
      //     : null,
    );

    SimpleAudioPlayerService.instance
      ..playMediaItem(mediaItem, playlistName: widget.playlistName)
      ..setPlayerSize(fullScreen: true);
  }

  void _playAllSongs(List songs) {
    print("songs---------------${songs.length}----------");

    if (songs.isEmpty) return;

    final items = songs.map((s) {
      return MediaItem(
        id: s.id.toString(),
        title: s.title ?? 'Unknown Title',
        path: s.path ??
            '', // تأكد أن التطبيق يتعامل مع المسارات الفارغة بشكل مناسب
        artist: s.artist ?? 'Unknown Artist',
        album: s.album ?? 'Unknown Album',
        // duration: Duration(milliseconds: s.duration ?? 0),
        type: MediaType.audio,
        size: s.size ?? 0,
        // dateAdded: DateTime.fromMillisecondsSinceEpoch(
        //   (s.dateAdded ?? DateTime.now().millisecondsSinceEpoch),
        // ),
      );
    }).toList();

    if (items.isEmpty) return;

    SimpleAudioPlayerService.instance
      ..playMediaItem(
        items.first,
        playlist: items,
        playlistName: widget.playlistName,
      )
      ..setPlayerSize(fullScreen: true);
  }

  // void _playAllSongs(List songs) {
  //   print("songs---------------{songs.length}----------");
  //   print("${songs.length}");
  //   if (songs.isEmpty) return;
  //   final items = songs
  //       .map((s) => MediaItem(
  //             id: s.id.toString(),
  //             title: s.title,
  //             path: s.path,
  //             artist: s.artist,
  //             album: s.album,
  //             duration: Duration(milliseconds: s.duration ?? 0),
  //             type: MediaType.audio,
  //             size: s.size,
  //             dateAdded: DateTime.fromMillisecondsSinceEpoch(s.dateAdded ?? 0),
  //           ))
  //       .toList();
  //   SimpleAudioPlayerService.instance
  //     ..playMediaItem(items.first,
  //         playlist: items, playlistName: widget.playlistName)
  //     ..setPlayerSize(fullScreen: true);
  // }

  void _showAddSongsDialog(AudioPlaylistController controller) =>
      Get.to(() => AddSongsToPlaylistPage(
          controller: controller, playlistIndex: widget.playlistIndex));
}
