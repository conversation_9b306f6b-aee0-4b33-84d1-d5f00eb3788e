import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:social_media_app/%20controllers/media_controller.dart';
import 'package:social_media_app/widgets/enhanced_full_player.dart';
import 'package:social_media_app/widgets/sort_filter_widget.dart';
import 'audio_home_page.dart';
import 'video/video_home_page.dart';
import 'playlist_page.dart';
import 'folders/folders_page.dart';
import 'settings/settings_page.dart';
import 'artists_page.dart';
import 'albums_page.dart';
import '../widgets/global_player_overlay.dart';

class MainNavigationPage extends StatefulWidget {
  const MainNavigationPage({super.key});

  @override
  State<MainNavigationPage> createState() => _MainNavigationPageState();
}

class _MainNavigationPageState extends State<MainNavigationPage>
    with TickerProviderStateMixin {
  late final TabController _tabController;

  final List<Tab> _tabs = const [
    Tab(icon: Icon(Icons.music_note, size: 30), text: 'الصوت'),
    Tab(icon: Icon(Icons.video_library, size: 30), text: 'الفيديو'),
    Tab(icon: Icon(Icons.playlist_play, size: 30), text: 'القوائم'),
    Tab(icon: Icon(Icons.folder, size: 30), text: 'المجلدات'),
    Tab(icon: Icon(Icons.person, size: 30), text: 'الفنانون'),
    Tab(icon: Icon(Icons.album, size: 30), text: 'الألبومات'),
  ];

  final List<Widget> _tabPages = const [
    AudioHomePage(),
    VideoHomePage(),
    PlaylistPage(),
    FoldersPage(),
    ArtistsPage(),
    AlbumsPage(),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Get.theme;

    return GlobalPlayerOverlay(
      child: Scaffold(
        body: SafeArea(
          child: Column(
            children: [
              // شريط الإعدادات والبحث
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.settings, size: 28),
                      onPressed: () {
                        Get.to(() => const SettingsPage());
                      },
                    ),
                    Text(
                      "الوسائط",
                      style: theme.textTheme.titleLarge
                          ?.copyWith(fontWeight: FontWeight.bold),
                    ),
                    IconButton(
                      icon: const Icon(Icons.search, size: 25),
                      onPressed: () {
                        // TODO: تنفيذ البحث
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.sort, size: 25),
                      onPressed: () {
                        showSortOptions(context, Get.find<MediaController>());
                        // TODO: الفرز
                      },
                    ),
                  ],
                ),
              ),

              // شريط التبويبات المحسن
              Container(
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 6,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: TabBar(
                  controller: _tabController,
                  isScrollable: true,
                  labelColor: theme.colorScheme.primary,
                  unselectedLabelColor:
                      theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  indicatorColor: theme.colorScheme.primary,
                  indicatorWeight: 3,
                  labelStyle: const TextStyle(
                      fontSize: 16, fontWeight: FontWeight.bold),
                  unselectedLabelStyle: const TextStyle(fontSize: 14),
                  tabs: _tabs,
                ),
              ),

              // المحتوى مع padding ذكي للمشغل
              Expanded(
                child: PlayerAwarePadding(
                  child: TabBarView(
                    controller: _tabController,
                    children: _tabPages,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// عرض خيارات الترتيب
  void showSortOptions(BuildContext context, MediaController mediaController) {
    SortFilterWidget.show(
      items: mediaController.allAudioFiles,
      onSorted: (sortedItems) {
        mediaController.allAudioFiles.assignAll(sortedItems);
      },
    );
  }
}
