import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../ controllers/playlist_controllervideo.dart';
import 'video_player_page.dart';

class PlaylistVideosPage extends StatelessWidget {
  final int playlistIndex;

  const PlaylistVideosPage({super.key, required this.playlistIndex});

  @override
  Widget build(BuildContext context) {
    final playlistController = Get.find<PlaylistController>();

    return Scaffold(
      appBar: AppBar(
        title: Obx(() {
          final playlists = playlistController.playlists;
          if (playlistIndex < playlists.length) {
            return Text(playlists[playlistIndex].name);
          }
          return const Text('قائمة التشغيل');
        }),
      ),
      body: Obx(() {
        final playlists = playlistController.playlists;
        if (playlistIndex >= playlists.length) {
          return const Center(child: Text('قائمة التشغيل غير موجودة'));
        }

        final videoPaths = playlists[playlistIndex].videoPaths;
        if (videoPaths.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.playlist_add, size: 50),
                const SizedBox(height: 20),
                const Text('لا توجد فيديوهات في هذه القائمة'),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    // يمكنك إضافة وظيفة لإضافة فيديوهات هنا
                  },
                  child: const Text('إضافة فيديوهات'),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: videoPaths.length,
          itemBuilder: (context, index) {
            final path = videoPaths[index];
            final file = File(path);

            if (!file.existsSync()) {
              playlistController.removeVideoFromPlaylist(playlistIndex, path);
              return const SizedBox.shrink();
            }

            return Card(
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: ListTile(
                leading: const Icon(Icons.video_file),
                title: Text(file.path.split('/').last),
                trailing: IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: () {
                    playlistController.removeVideoFromPlaylist(playlistIndex, path);
                    Get.snackbar(
                      'تم الحذف',
                      'تم حذف الفيديو من قائمة التشغيل',
                      snackPosition: SnackPosition.BOTTOM,
                    );
                  },
                ),
                onTap: () {
                  Get.to(() => VideoPlayerPage(videoFile: file));
                },
              ),
            );
          },
        );
      }),
    );
  }
}