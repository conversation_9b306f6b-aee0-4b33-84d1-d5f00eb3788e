import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
// import 'package:url_launcher/url_launcher.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../ controllers/audio_controller.dart';
import '../../ controllers/media_controller.dart';
import '../../ controllers/settings_controller.dart';
import '../../ controllers/theme_controller.dart';
import '../../widgets/global_player_overlay.dart';

/// صفحة الإعدادات الرئيسية للتطبيق
/// تحتوي على جميع إعدادات التطبيق والثيمات والتفضيلات
class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    // الحصول على الكنترولرز المطلوبة
    final themeController = Get.find<ThemeController>(); // كنترولر الثيمات
    final settingsController =
        Get.put(SettingsController()); // كنترولر الإعدادات
    final audioController = Get.find<AudioController>(); // كنترولر الصوت
    final mediaController = Get.find<MediaController>(); // كنترولر الوسائط

    return GlobalPlayerOverlay(
      child: Scaffold(
        // استخدام لون الخلفية من الثيم الحالي
        backgroundColor: Get.theme.scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Get.theme.colorScheme.onSurface),
          onPressed: () => Get.back(),
        ),
        title: Text(
          'الإعدادات',
          style: TextStyle(
            color: Get.theme.colorScheme.onSurface,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // قسم البطاقات الشبكية - يحتوي على الإعدادات الرئيسية
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.2,
              children: [
                // بطاقة المظهر - تعرض الثيم الحالي وتسمح بتغييره
                Obx(() => _buildGridCard(
                      icon: Icons.palette,
                      title: 'المظهر',
                      subtitle: themeController.currentThemeName.value,
                      color: Get.theme.colorScheme.primary,
                      onTap: () => _showThemeDialog(context, themeController),
                    )),
                // بطاقة مؤقت النوم - تعرض الوقت المحدد للإيقاف التلقائي
                Obx(() => _buildGridCard(
                      icon: Icons.access_time,
                      title: 'مؤقت النوم',
                      subtitle: settingsController.sleepTimerText.value,
                      color: Colors.deepOrange,
                      onTap: () =>
                          _showSleepTimerDialog(context, settingsController),
                    )),
                // بطاقة النسخ الاحتياطي - لحفظ البيانات
                _buildGridCard(
                  icon: Icons.backup,
                  title: 'نسخ احتياطي',
                  subtitle: 'احصل على 15 جيجا',
                  color: Colors.blue,
                  onTap: () => _showBackupDialog(context),
                ),
                // بطاقة إزالة الإعلانات - للاشتراك المدفوع
                _buildGridCard(
                  icon: Icons.block,
                  title: 'إزالة الإعلانات',
                  subtitle: 'خصم 50%',
                  color: Colors.red,
                  onTap: () => _showRemoveAdsDialog(context),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // قسم الإحصائيات - يعرض معلومات الاستخدام والملفات
            Container(
              decoration: BoxDecoration(
                color: Get.theme.cardColor,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  // عنصر وقت التشغيل الإجمالي
                  Obx(() => _buildListTile(
                        icon: Icons.play_circle,
                        title: 'وقت التشغيل',
                        trailing: settingsController.totalPlayTime.value,
                        onTap: () =>
                            _showPlaytimeDetails(context, settingsController),
                      )),
                  _buildDivider(),
                  // عنصر الملفات المخفية
                  Obx(() => _buildListTile(
                        icon: Icons.visibility_off,
                        title: 'الملفات المخفية',
                        trailing:
                            '${settingsController.hiddenFilesCount.value} ملف',
                        onTap: () => _showHiddenFiles(context),
                      )),
                  _buildDivider(),
                  // عنصر الملفات المحذوفة مؤخراً
                  Obx(() => _buildListTile(
                        icon: Icons.delete,
                        title: 'المحذوفة مؤخراً',
                        trailing:
                            '${settingsController.deletedFilesCount.value} ملف',
                        onTap: () => _showRecentlyDeleted(context),
                      )),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // قسم الإعدادات الرئيسية - يحتوي على إعدادات التشغيل واللغة
            Container(
              decoration: BoxDecoration(
                color: Get.theme.cardColor,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  // عنصر إعدادات التشغيل
                  _buildListTile(
                    icon: Icons.headphones,
                    title: 'إعدادات التشغيل',
                    trailing: '',
                    onTap: () =>
                        _showPlaybackSettings(context, audioController),
                  ),
                  _buildDivider(),
                  // عنصر إعدادات الإشعارات
                  _buildListTile(
                    icon: Icons.notifications,
                    title: 'إعدادات الإشعارات',
                    trailing: '',
                    onTap: () =>
                        _showNotificationSettings(context, settingsController),
                  ),
                  _buildDivider(),
                  // عنصر اختيار اللغة
                  Obx(() => _buildListTile(
                        icon: Icons.language,
                        title: 'اللغة',
                        trailing: settingsController.currentLanguage.value,
                        onTap: () =>
                            _showLanguageDialog(context, settingsController),
                      )),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // قسم الخيارات الإضافية - يحتوي على التعليقات والمشاركة والمعلومات
            Container(
              decoration: BoxDecoration(
                color: Get.theme.cardColor,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  // عنصر إرسال التعليقات
                  _buildListTile(
                    icon: Icons.feedback,
                    title: 'التعليقات',
                    trailing: '',
                    onTap: () => _sendFeedback(),
                  ),
                  _buildDivider(),
                  // عنصر تقييم التطبيق
                  _buildListTile(
                    icon: Icons.star_rate,
                    title: 'قيمنا',
                    trailing: '',
                    onTap: () => _rateApp(),
                  ),
                  _buildDivider(),
                  // عنصر مشاركة التطبيق
                  _buildListTile(
                    icon: Icons.share,
                    title: 'مشاركة التطبيق',
                    trailing: '',
                    onTap: () => _shareApp(),
                  ),
                  _buildDivider(),
                  // عنصر سياسة الخصوصية
                  _buildListTile(
                    icon: Icons.privacy_tip,
                    title: 'سياسة الخصوصية',
                    trailing: '',
                    onTap: () => _openPrivacyPolicy(),
                  ),
                  _buildDivider(),
                  // عنصر شروط الخدمة
                  _buildListTile(
                    icon: Icons.description,
                    title: 'شروط الخدمة',
                    trailing: '',
                    onTap: () => _openTermsOfService(),
                  ),
                  _buildDivider(),
                  // عنصر معلومات التطبيق
                  _buildListTile(
                    icon: Icons.info,
                    title: 'حول التطبيق',
                    trailing: '',
                    onTap: () => _showAboutDialog(context),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 100),
          ],
        ),
      ),
    );
  }

  /// دالة بناء بطاقة شبكية
  /// تستخدم لإنشاء البطاقات في الشبكة العلوية
  /// [icon] أيقونة البطاقة
  /// [title] عنوان البطاقة
  /// [subtitle] النص الفرعي
  /// [color] لون البطاقة
  /// [onTap] دالة النقر
  Widget _buildGridCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[900],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.grey[800]!),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const Spacer(),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// دالة بناء عنصر قائمة
  /// تستخدم لإنشاء عناصر القائمة في الأقسام المختلفة
  /// [icon] أيقونة العنصر
  /// [title] عنوان العنصر
  /// [trailing] النص الجانبي (اختياري)
  /// [onTap] دالة النقر (اختيارية)
  Widget _buildListTile({
    required IconData icon,
    required String title,
    required String trailing,
    VoidCallback? onTap,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: Colors.grey[400], size: 20),
      ),
      title: Text(
        title,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
        ),
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (trailing.isNotEmpty)
            Text(
              trailing,
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 14,
              ),
            ),
          const SizedBox(width: 8),
          Icon(Icons.arrow_forward_ios, color: Colors.grey[600], size: 16),
        ],
      ),
      onTap: onTap,
    );
  }

  /// دالة بناء خط فاصل
  /// تستخدم لفصل عناصر القائمة عن بعضها البعض
  Widget _buildDivider() {
    return Divider(
      color: Get.theme.dividerColor,
      height: 1,
      indent: 72,
    );
  }

  /// دالة عرض حوار اختيار الثيم
  /// تعرض قائمة بالثيمات المتاحة ويمكن للمستخدم اختيار أحدها
  /// [context] سياق البناء
  /// [controller] كنترولر الثيمات
  void _showThemeDialog(BuildContext context, ThemeController controller) {
    Get.dialog(
      Dialog(
        backgroundColor: Get.theme.dialogBackgroundColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'اختر المظهر',
                style: TextStyle(
                  color: Get.theme.colorScheme.onSurface,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              ...controller.availableThemes.map(
                (theme) => Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                    color: Get.theme.cardColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ListTile(
                    leading: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            theme['color'],
                            theme['color'].withValues(alpha: 0.7)
                          ],
                        ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                    title: Text(
                      theme['title'],
                      style: TextStyle(color: Get.theme.colorScheme.onSurface),
                    ),
                    trailing: Obx(() => controller.currentTheme == theme['name']
                        ? const Icon(Icons.check_circle, color: Colors.green)
                        : const Icon(Icons.radio_button_unchecked,
                            color: Colors.grey)),
                    onTap: () {
                      controller.changeTheme(theme['name']);
                      Get.back();
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// دالة عرض حوار مؤقت النوم
  /// تعرض خيارات مختلفة لتعيين مؤقت إيقاف التشغيل التلقائي
  /// [context] سياق البناء
  /// [controller] كنترولر الإعدادات
  void _showSleepTimerDialog(
      BuildContext context, SettingsController controller) {
    Get.dialog(
      Dialog(
        backgroundColor: Get.theme.dialogBackgroundColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'مؤقت النوم',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              ...[
                'إيقاف',
                '15 دقيقة',
                '30 دقيقة',
                '45 دقيقة',
                '60 دقيقة',
                '90 دقيقة'
              ].map(
                (time) => Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[800],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ListTile(
                    title: Text(
                      time,
                      style: const TextStyle(color: Colors.white),
                    ),
                    trailing: Obx(() => controller.sleepTimerText.value == time
                        ? const Icon(Icons.check_circle, color: Colors.green)
                        : const Icon(Icons.radio_button_unchecked,
                            color: Colors.grey)),
                    onTap: () {
                      controller.setSleepTimer(time);
                      Get.back();
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Playback Settings Dialog
  void _showPlaybackSettings(BuildContext context, AudioController controller) {
    Get.dialog(
      Dialog(
        backgroundColor: Colors.grey[900],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'إعدادات التشغيل',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),

              // Auto Play Next
              Obx(() => SwitchListTile(
                    title: const Text('التشغيل التلقائي للتالي',
                        style: TextStyle(color: Colors.white)),
                    value: controller.autoPlayNext.value,
                    onChanged: (value) => controller.toggleAutoPlayNext(),
                    activeColor: Colors.orange,
                  )),

              // Shuffle Mode
              Obx(() => SwitchListTile(
                    title: const Text('التشغيل العشوائي',
                        style: TextStyle(color: Colors.white)),
                    value: controller.isShuffleEnabled.value,
                    onChanged: (value) => controller.toggleShuffle(),
                    activeColor: Colors.orange,
                  )),

              // Repeat Mode
              ListTile(
                title: const Text('وضع التكرار',
                    style: TextStyle(color: Colors.white)),
                trailing: Obx(() => DropdownButton<String>(
                      value: controller.repeatMode.value,
                      dropdownColor: Colors.grey[800],
                      style: const TextStyle(color: Colors.white),
                      items: const [
                        DropdownMenuItem(
                            value: 'none', child: Text('بدون تكرار')),
                        DropdownMenuItem(
                            value: 'one', child: Text('تكرار الحالي')),
                        DropdownMenuItem(
                            value: 'all', child: Text('تكرار الكل')),
                      ],
                      onChanged: (value) => controller.setRepeatMode(value!),
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Language Dialog
  void _showLanguageDialog(
      BuildContext context, SettingsController controller) {
    Get.dialog(
      Dialog(
        backgroundColor: Colors.grey[900],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'اختر اللغة',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              ...['العربية', 'English', 'Français', 'Español'].map(
                (language) => Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[800],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ListTile(
                    title: Text(
                      language,
                      style: const TextStyle(color: Colors.white),
                    ),
                    trailing: Obx(() => controller.currentLanguage.value ==
                            language
                        ? const Icon(Icons.check_circle, color: Colors.green)
                        : const Icon(Icons.radio_button_unchecked,
                            color: Colors.grey)),
                    onTap: () {
                      controller.setLanguage(language);
                      Get.back();
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Notification Settings
  void _showNotificationSettings(
      BuildContext context, SettingsController controller) {
    Get.dialog(
      Dialog(
        backgroundColor: Colors.grey[900],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'إعدادات الإشعارات',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              Obx(() => SwitchListTile(
                    title: const Text('إشعارات التشغيل',
                        style: TextStyle(color: Colors.white)),
                    subtitle: const Text('عرض إشعار أثناء تشغيل الوسائط',
                        style: TextStyle(color: Colors.grey)),
                    value: controller.playbackNotifications.value,
                    onChanged: (value) =>
                        controller.togglePlaybackNotifications(),
                    activeColor: Colors.orange,
                  )),
              Obx(() => SwitchListTile(
                    title: const Text('إشعارات النظام',
                        style: TextStyle(color: Colors.white)),
                    subtitle: const Text('إشعارات التحديثات والأخبار',
                        style: TextStyle(color: Colors.grey)),
                    value: controller.systemNotifications.value,
                    onChanged: (value) =>
                        controller.toggleSystemNotifications(),
                    activeColor: Colors.orange,
                  )),
            ],
          ),
        ),
      ),
    );
  }

  // Additional Methods
  void _showBackupDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text('النسخ الاحتياطي',
            style: TextStyle(color: Colors.white)),
        content: const Text(
          'هل تريد إنشاء نسخة احتياطية من قوائم التشغيل والإعدادات؟',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              Get.snackbar('نجح', 'تم إنشاء النسخة الاحتياطية بنجاح');
            },
            child: const Text('نسخ احتياطي'),
          ),
        ],
      ),
    );
  }

  void _showRemoveAdsDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text('إزالة الإعلانات',
            style: TextStyle(color: Colors.white)),
        content: const Text(
          'احصل على تجربة خالية من الإعلانات مع خصم 50%',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('لاحقاً'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              // Navigate to purchase screen
            },
            child: const Text('اشتراك الآن'),
          ),
        ],
      ),
    );
  }

  void _showPlaytimeDetails(
      BuildContext context, SettingsController controller) {
    Get.dialog(
      Dialog(
        backgroundColor: Colors.grey[900],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'إحصائيات التشغيل',
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 20),
              _buildStatItem(
                  'إجمالي وقت التشغيل', controller.totalPlayTime.value),
              _buildStatItem(
                  'الأغاني المشغلة', '${controller.songsPlayed.value} أغنية'),
              _buildStatItem('الفيديوهات المشاهدة',
                  '${controller.videosWatched.value} فيديو'),
              _buildStatItem('المتوسط اليومي', controller.dailyAverage.value),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(String title, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: const TextStyle(color: Colors.white70)),
          Text(value,
              style: const TextStyle(
                  color: Colors.white, fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  void _showHiddenFiles(BuildContext context) {
    // Navigate to hidden files page
    Get.snackbar('قريباً', 'ستتوفر هذه الميزة قريباً');
  }

  void _showRecentlyDeleted(BuildContext context) {
    // Navigate to recently deleted page
    Get.snackbar('قريباً', 'ستتوفر هذه الميزة قريباً');
  }

  // دالة إرسال التعليقات - تفتح تطبيق البريد الإلكتروني
  void _sendFeedback() async {
    Get.snackbar(
      'التعليقات',
      'سيتم فتح تطبيق البريد الإلكتروني قريباً',
      backgroundColor: Get.theme.colorScheme.primary,
      colorText: Colors.white,
    );
  }

  // دالة تقييم التطبيق - تفتح متجر التطبيقات
  void _rateApp() async {
    Get.snackbar(
      'التقييم',
      'سيتم فتح متجر التطبيقات قريباً',
      backgroundColor: Get.theme.colorScheme.primary,
      colorText: Colors.white,
    );
  }

  // دالة مشاركة التطبيق - تفتح خيارات المشاركة
  void _shareApp() {
    Share.share(
      'جرب هذا التطبيق الرائع لتشغيل الوسائط!\nhttps://play.google.com/store/apps/details?id=com.example.mediaplayerapp',
      subject: 'تطبيق مشغل الوسائط',
    );
  }

  // دالة فتح سياسة الخصوصية
  void _openPrivacyPolicy() async {
    Get.snackbar(
      'سياسة الخصوصية',
      'سيتم فتح الرابط قريباً',
      backgroundColor: Get.theme.colorScheme.primary,
      colorText: Colors.white,
    );
  }

  // دالة فتح شروط الخدمة
  void _openTermsOfService() async {
    Get.snackbar(
      'شروط الخدمة',
      'سيتم فتح الرابط قريباً',
      backgroundColor: Get.theme.colorScheme.primary,
      colorText: Colors.white,
    );
  }

  void _showAboutDialog(BuildContext context) async {
    final packageInfo = await PackageInfo.fromPlatform();

    Get.dialog(
      AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text('حول التطبيق', style: TextStyle(color: Colors.white)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('اسم التطبيق: ${packageInfo.appName}',
                style: const TextStyle(color: Colors.white70)),
            Text('الإصدار: ${packageInfo.version}',
                style: const TextStyle(color: Colors.white70)),
            Text('رقم البناء: ${packageInfo.buildNumber}',
                style: const TextStyle(color: Colors.white70)),
            const SizedBox(height: 10),
            const Text(
              'تطبيق متقدم لتشغيل الوسائط مع دعم كامل للصوت والفيديو',
              style: TextStyle(color: Colors.white70),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
