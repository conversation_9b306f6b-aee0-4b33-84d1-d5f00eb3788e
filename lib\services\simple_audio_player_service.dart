// تحسينات بصرية مع الحفاظ على كامل وظائف الخدمة
import 'dart:io';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import 'package:audio_session/audio_session.dart';
import 'package:social_media_app/model/media_item.dart';
import '../model/media_item.dart' as local;

class SimpleAudioPlayerService extends GetxController {
  static SimpleAudioPlayerService get instance =>
      Get.find<SimpleAudioPlayerService>();

  final AudioPlayer _audioPlayer = AudioPlayer();

  // الحالات
  var isPlaying = false.obs;
  var isLoading = false.obs;
  var currentPosition = Duration.zero.obs;
  var totalDuration = Duration.zero.obs;
  var playbackSpeed = 1.0.obs;
  final _currentIndex = 0.obs;

  var isPlayerVisible = true.obs;
  var isMinimized = false.obs;
  var isFullScreen = true.obs;

  var currentPlaylist = <local.MediaItem>[].obs;
  var currentIndex = 0.obs;
  var currentMediaItem = Rxn<local.MediaItem>();

  var repeatMode = RepeatMode.none.obs;
  var isShuffleEnabled = false.obs;
  var shuffledIndices = <int>[].obs;

  var sortOrder = SortOrder.name.obs;
  var isAscending = true.obs;
  var currentPlaylistName = 'جميع الأغاني'.obs;
  var currentPlaylistId = ''.obs;

  String _lastPlayedTrackId = '';
  DateTime _lastPlayTime = DateTime.now();

  @override
  void onInit() {
    super.onInit();
    _initializePlayer();
  }

  @override
  void onClose() {
    _audioPlayer.dispose();
    super.onClose();
  }

  Future<void> _initializePlayer() async {
    try {
      final session = await AudioSession.instance;
      await session.configure(const AudioSessionConfiguration.music());
      _setupStreams();
    } catch (e) {
      print('خطأ في تهيئة المشغل: $e');
    }
  }

  void _setupStreams() {
    _audioPlayer.playingStream.listen((playing) => isPlaying.value = playing);
    _audioPlayer.positionStream
        .listen((position) => currentPosition.value = position);
    _audioPlayer.durationStream.listen((duration) {
      if (duration != null) totalDuration.value = duration;
    });
    _audioPlayer.playerStateStream.listen((state) {
      isLoading.value = state.processingState == ProcessingState.loading;
      if (state.processingState == ProcessingState.completed)
        _onTrackCompleted();
    });
  }

  void _onTrackCompleted() {
    switch (repeatMode.value) {
      case RepeatMode.one:
        _audioPlayer.seek(Duration.zero);
        _audioPlayer.play();
        break;
      case RepeatMode.all:
        playNext();
        break;
      case RepeatMode.none:
        if (currentIndex.value < currentPlaylist.length - 1) {
          playNext();
        } else {
          stop();
        }
        break;
    }
  }

  Future<void> playMediaItem(local.MediaItem item,
      {List<local.MediaItem>? playlist, String? playlistName}) async {
    try {
      isLoading.value = true;
      final file = File(item.path);
      if (!await file.exists()) {
        Get.snackbar('خطأ', 'الملف غير موجود: ${item.title}',
            snackPosition: SnackPosition.BOTTOM);
        return;
      }

      if (playlist != null) {
        currentPlaylist.assignAll(playlist);
        currentIndex.value =
            playlist.indexWhere((media) => media.id == item.id);
        if (currentIndex.value == -1) currentIndex.value = 0;
      } else {
        currentPlaylist.assignAll([item]);
        currentIndex.value = 0;
      }

      currentPlaylistName.value = playlistName ?? 'جميع الأغاني';
      currentPlaylistId.value = playlistName ?? '';
      currentMediaItem.value = item;

      await _audioPlayer.stop();
      final uri = Uri.file(item.path);

      try {
        await _audioPlayer.setAudioSource(AudioSource.uri(uri), preload: false);
        await _audioPlayer.play();
      } catch (_) {
        await _audioPlayer.setFilePath(item.path);
        await _audioPlayer.play();
      }

      showPlayer();
    } catch (e) {
      print('خطأ في التشغيل: $e');
      Get.snackbar('خطأ في التشغيل', '${item.title}\n${e.toString()}',
          snackPosition: SnackPosition.BOTTOM);
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> togglePlayPause() async {
    isPlaying.value ? await _audioPlayer.pause() : await _audioPlayer.play();
  }

  Future<void> playNext() async {
    if (currentPlaylist.isEmpty) return;
    final nextIndex = isShuffleEnabled.value
        ? _getNextShuffledIndex()
        : (currentIndex.value + 1) % currentPlaylist.length;
    await safePlayAtIndex(nextIndex);
  }

  Future<void> playPrevious() async {
    if (currentPlaylist.isEmpty) return;
    int prevIndex = isShuffleEnabled.value
        ? _getPreviousShuffledIndex()
        : (currentIndex.value - 1 + currentPlaylist.length) %
            currentPlaylist.length;
    await safePlayAtIndex(prevIndex);
  }

  Future<void> seekTo(Duration position) async =>
      await _audioPlayer.seek(position);

  Future<void> setPlaybackSpeed(double speed) async {
    playbackSpeed.value = speed;
    await _audioPlayer.setSpeed(speed);
  }

  Future<void> toggleRepeatMode() async {
    repeatMode.value = RepeatMode
        .values[(repeatMode.value.index + 1) % RepeatMode.values.length];
  }

  Future<void> toggleShuffle() async {
    isShuffleEnabled.toggle();
    if (isShuffleEnabled.value) _generateShuffledIndices();
  }

  void _generateShuffledIndices() {
    shuffledIndices
        .assignAll(List.generate(currentPlaylist.length, (i) => i)..shuffle());
  }

  int _getNextShuffledIndex() {
    if (shuffledIndices.isEmpty) _generateShuffledIndices();
    final currentShuffledIndex = shuffledIndices.indexOf(currentIndex.value);
    return shuffledIndices[(currentShuffledIndex + 1) % shuffledIndices.length];
  }

  int _getPreviousShuffledIndex() {
    if (shuffledIndices.isEmpty) _generateShuffledIndices();
    final currentShuffledIndex = shuffledIndices.indexOf(currentIndex.value);
    return shuffledIndices[(currentShuffledIndex - 1 + shuffledIndices.length) %
        shuffledIndices.length];
  }

  Future<void> stop() async => await _audioPlayer.stop();

  void showPlayer({bool fullScreen = true}) {
    isPlayerVisible.value = true;
    isFullScreen.value = fullScreen;
    isMinimized.value = !fullScreen;
  }

  void hidePlayer() {
    isPlayerVisible.value = false;
    isMinimized.value = true;
    isFullScreen.value = false;
  }  void hidePlayer1() {
    // isPlayerVisible.value = false;
    isMinimized.value = true;
    isFullScreen.value = false;
  }

  void togglePlayerSize() => setPlayerSize(fullScreen: !isFullScreen.value);

  void setPlayerSize({required bool fullScreen}) {
    isFullScreen.value = fullScreen;
    isMinimized.value = !fullScreen;
  }

  void sortPlaylist(SortOrder order) {
    currentPlaylist.sort((a, b) => (a.title ?? '').compareTo(b.title ?? ''));
  }

  Future<void> safePlayAtIndex(int index) async {
    if (currentPlaylist.isEmpty) return;
    final safeIndex = index.clamp(0, currentPlaylist.length - 1);
    if (currentIndex.value != safeIndex) {
      currentIndex.value = safeIndex;
      currentMediaItem.value = currentPlaylist[safeIndex];
      await _playDirectly(currentPlaylist[safeIndex]);
    }
  }

  Future<void> _playDirectly(local.MediaItem item) async {
    final now = DateTime.now();
    if (_lastPlayedTrackId == item.id &&
        now.difference(_lastPlayTime).inMilliseconds < 1000) return;

    try {
      isLoading.value = true;
      _lastPlayedTrackId = item.id ?? '';
      _lastPlayTime = now;

      await _audioPlayer.stop();

      try {
        await _audioPlayer.setAudioSource(AudioSource.uri(Uri.file(item.path)),
            preload: false);
        await _audioPlayer.play();
      } catch (_) {
        await _audioPlayer.setFilePath(item.path);
        await _audioPlayer.play();
      }

      // showPlayer(fullScreen: true);
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في التشغيل: ${item.title}',
          snackPosition: SnackPosition.BOTTOM);
    } finally {
      isLoading.value = false;
    }
  }

  local.MediaItem? safeGetItemAtIndex(int index) {
    if (index < 0 || index >= currentPlaylist.length) return null;
    return currentPlaylist[index];
  }

//
  /// Restore the original order (clear any shuffle)
  void restoreOriginalOrder() {
    if (_originalOrder == null) return;
    currentPlaylist.assignAll(_originalOrder!);
    _shuffledOrder = null;
    _currentIndex.value = 0;
    update(); // triggers Obx/Builder
  }

  /// Shuffle the list instantly
  void shufflePlaylist() {
    _originalOrder ??= List.of(currentPlaylist); // keep a copy if not yet saved
    currentPlaylist.shuffle();
    _currentIndex.value = 0;
    update();
  }

/* Private fields */
  List<MediaItem>? _originalOrder;
  List<MediaItem>? _shuffledOrder;
//

  void validateAndFixCurrentIndex() {
    if (currentPlaylist.isEmpty) {
      currentIndex.value = 0;
      currentMediaItem.value = null;
    } else if (currentIndex.value < 0 ||
        currentIndex.value >= currentPlaylist.length) {
      currentIndex.value = 0;
      currentMediaItem.value = currentPlaylist.first;
    }
  }

  String get currentTitle => currentMediaItem.value?.title ?? '';
  String get currentArtist => currentMediaItem.value?.artist ?? 'مجهول';
  String get currentAlbum => currentMediaItem.value?.album ?? 'مجهول';
  bool get hasCurrentTrack => currentMediaItem.value != null;
  String get formattedPosition => _formatDuration(currentPosition.value);
  String get formattedDuration => _formatDuration(totalDuration.value);

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}

enum RepeatMode { none, one, all }

enum SortOrder { name, artist, album, duration, dateAdded }
