// import 'package:get/get.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'dart:convert';
// import '../models/database_models.dart';
// import '../model/media_item.dart';

// /// خدمة قاعدة البيانات المحلية المبسطة
// class DatabaseService extends GetxController {
//   static DatabaseService get instance => Get.find<DatabaseService>();

//   late SharedPreferences _prefs;

//   // قوائم تفاعلية
//   var favorites = <FavoriteItem>[].obs;
//   var hiddenItems = <HiddenItem>[].obs;
//   var appSettings = Rxn<AppSettings>();

//   @override
//   Future<void> onInit() async {
//     super.onInit();
//     await _initializePrefs();
//     await _loadData();
//   }

//   /// تهيئة SharedPreferences
//   Future<void> _initializePrefs() async {
//     _prefs = await SharedPreferences.getInstance();
//   }

//   /// تحميل البيانات
//   Future<void> _loadData() async {
//     try {
//       // تحميل المفضلة
//       final favoritesJson = _prefs.getStringList('favorites') ?? [];
//       favorites.assignAll(
//         favoritesJson.map((json) => FavoriteItem.fromMap(jsonDecode(json))).toList(),
//       );

//       // تحميل الملفات المخفية
//       final hiddenJson = _prefs.getStringList('hidden') ?? [];
//       hiddenItems.assignAll(
//         hiddenJson.map((json) => HiddenItem.fromMap(jsonDecode(json))).toList(),
//       );

//       // تحميل الإعدادات
//       appSettings.value = AppSettings();
//     } catch (e) {
//       print('خطأ في تحميل البيانات: $e');
//     }
//   }

//   // === إدارة المفضلة ===

//   /// إضافة إلى المفضلة
//   Future<bool> addToFavorites(MediaItem mediaItem) async {
//     try {
//       // التحقق من عدم وجود العنصر مسبقاً
//       if (isFavorite(mediaItem.id ?? '')) {
//         return false;
//       }

//       final favoriteItem = FavoriteItem.fromMediaItem(mediaItem);
//       favorites.add(favoriteItem);
//       await _saveFavorites();
//       return true;
//     } catch (e) {
//       print('خطأ في إضافة المفضلة: $e');
//       return false;
//     }
//   }

//   /// إزالة من المفضلة
//   Future<bool> removeFromFavorites(String itemId) async {
//     try {
//       favorites.removeWhere((item) => item.id == itemId);
//       await _saveFavorites();
//       return true;
//     } catch (e) {
//       print('خطأ في إزالة المفضلة: $e');
//       return false;
//     }
//   }

//   /// حفظ المفضلة
//   Future<void> _saveFavorites() async {
//     try {
//       final favoritesJson = favorites.map((item) => jsonEncode(item.toMap())).toList();
//       await _prefs.setStringList('favorites', favoritesJson);
//     } catch (e) {
//       print('خطأ في حفظ المفضلة: $e');
//     }
//   }

//   /// التحقق من وجود العنصر في المفضلة
//   bool isFavorite(String itemId) {
//     return favorites.any((item) => item.id == itemId);
//   }

//   /// الحصول على المفضلة حسب النوع
//   List<FavoriteItem> getFavoritesByType(String type) {
//     return favorites.where((item) => item.type == type).toList();
//   }

//   // === إدارة الملفات المخفية ===

//   /// إخفاء ملف
//   Future<bool> hideItem(MediaItem mediaItem) async {
//     try {
//       // التحقق من عدم إخفاء العنصر مسبقاً
//       if (isHidden(mediaItem.id ?? '')) {
//         return false;
//       }

//       final hiddenItem = HiddenItem.fromMediaItem(mediaItem);
//       hiddenItems.add(hiddenItem);
//       await _saveHidden();
//       return true;
//     } catch (e) {
//       print('خطأ في إخفاء الملف: $e');
//       return false;
//     }
//   }

//   /// إظهار ملف مخفي
//   Future<bool> unhideItem(String itemId) async {
//     try {
//       hiddenItems.removeWhere((item) => item.id == itemId);
//       await _saveHidden();
//       return true;
//     } catch (e) {
//       print('خطأ في إظهار الملف: $e');
//       return false;
//     }
//   }

//   /// حفظ الملفات المخفية
//   Future<void> _saveHidden() async {
//     try {
//       final hiddenJson = hiddenItems.map((item) => jsonEncode(item.toMap())).toList();
//       await _prefs.setStringList('hidden', hiddenJson);
//     } catch (e) {
//       print('خطأ في حفظ الملفات المخفية: $e');
//     }
//   }

//   /// التحقق من إخفاء العنصر
//   bool isHidden(String itemId) {
//     return hiddenItems.any((item) => item.id == itemId);
//   }

//   /// الحصول على الملفات المخفية حسب النوع
//   List<HiddenItem> getHiddenByType(String type) {
//     return hiddenItems.where((item) => item.type == type).toList();
//   }

//   // === وظائف إضافية ===

//   /// تسجيل تشغيل ملف (مبسط)
//   Future<void> recordPlayback(String itemId) async {
//     try {
//       // يمكن إضافة تسجيل الإحصائيات لاحقاً
//       print('تم تشغيل الملف: $itemId');
//     } catch (e) {
//       print('خطأ في تسجيل الإحصائيات: $e');
//     }
//   }

//   /// مسح جميع البيانات
//   Future<void> clearAllData() async {
//     try {
//       favorites.clear();
//       hiddenItems.clear();
//       await _prefs.remove('favorites');
//       await _prefs.remove('hidden');
//     } catch (e) {
//       print('خطأ في مسح البيانات: $e');
//     }
//   }

//   /// الحصول على إحصائيات سريعة
//   Map<String, int> getQuickStats() {
//     return {
//       'favorites': favorites.length,
//       'hidden': hiddenItems.length,
//       'favoriteAudio': getFavoritesByType('audio').length,
//       'favoriteVideo': getFavoritesByType('video').length,
//       'hiddenAudio': getHiddenByType('audio').length,
//       'hiddenVideo': getHiddenByType('video').length,
//     };
//   }
// }
