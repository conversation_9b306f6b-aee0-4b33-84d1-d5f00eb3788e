import 'package:flutter/material.dart';

class AppThemes {
  // --- Colors ---
  static const Color _darkScaffold = Color(0xFF121212);
  static const Color _amoledScaffold = Color(0xFF000000);
  static const Color _lightScaffold = Color(0xFFFFFFFF);

  // --- Private Helper Methods ---
  static ThemeData _buildDarkTheme(Color seedColor, {bool amoled = false}) {
    final base = ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: ColorScheme.fromSeed(
        seedColor: seedColor,
        brightness: Brightness.dark,
        surface: amoled ? _amoledScaffold : _darkScaffold,
      ),
      scaffoldBackgroundColor: amoled ? _amoledScaffold : _darkScaffold,
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: Colors.transparent,
        elevation: 0,
        type: BottomNavigationBarType.fixed,
        selectedLabelStyle: TextStyle(fontWeight: FontWeight.w600),
        unselectedLabelStyle: TextStyle(fontWeight: FontWeight.w500),
      ),
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        margin: const EdgeInsets.all(8),
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
        displayMedium: TextStyle(fontSize: 28, fontWeight: FontWeight.bold),
        headlineMedium: TextStyle(fontSize: 22, fontWeight: FontWeight.w600),
        titleMedium: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        bodyMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
      ),
    );

    return base;
  }

  static ThemeData _buildLightTheme(Color seedColor) {
    final base = ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: ColorScheme.fromSeed(
        seedColor: seedColor,
        brightness: Brightness.light,
        surface: _lightScaffold,
      ),
      scaffoldBackgroundColor: _lightScaffold,
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Colors.black,
        ),
      ),
      cardTheme: CardTheme(
        elevation: 2,
        color: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        margin: const EdgeInsets.all(8),
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
        displayMedium: TextStyle(fontSize: 28, fontWeight: FontWeight.bold),
        headlineMedium: TextStyle(fontSize: 22, fontWeight: FontWeight.w600),
        titleMedium: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        bodyMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
      ),
    );

    return base;
  }

  // --- Themes ---
  static final ThemeData darkTheme = _buildDarkTheme(Colors.purple);
  static final ThemeData lightTheme = _buildLightTheme(Colors.blue);
  static final ThemeData purpleTheme = _buildDarkTheme(Colors.deepPurple);
  static final ThemeData greenTheme = _buildDarkTheme(Colors.green);
  static final ThemeData orangeTheme = _buildDarkTheme(Colors.orange);
  static final ThemeData blueTheme = _buildDarkTheme(Colors.blue);
  static final ThemeData redTheme = _buildDarkTheme(Colors.red);
  static final ThemeData amoledTheme =
      _buildDarkTheme(Colors.blue, amoled: true);
  static final ThemeData highContrastTheme = ThemeData.from(
    useMaterial3: true,
    colorScheme: ColorScheme.highContrastDark(
      primary: Colors.white,
      onPrimary: Colors.black,
      surface: Colors.black,
      onSurface: Colors.white,
      secondary: Colors.yellow,
      onSecondary: Colors.black,
    ),
  ).copyWith(
    scaffoldBackgroundColor: Colors.black,
    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.black,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: TextStyle(
        color: Colors.white,
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    ),
  );
}
