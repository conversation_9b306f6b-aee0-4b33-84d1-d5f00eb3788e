import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../widgets/global_player_overlay.dart';

/// مساعد التنقل مع دعم المشغل العالمي
class NavigationHelper {
  /// التنقل إلى صفحة جديدة مع الحفاظ على المشغل
  static Future<T?> navigateToPage<T>(Widget page) {
    return Get.to<T>(() => GlobalPlayerOverlay(child: page));
  }

  /// التنقل إلى صفحة جديدة واستبدال الحالية مع الحفاظ على المشغل
  static Future<T?> navigateAndReplace<T>(Widget page) {
    return Get.off<T>(() => GlobalPlayerOverlay(child: page));
  }

  /// التنقل إلى صفحة جديدة وإزالة كل الصفحات السابقة مع الحفاظ على المشغل
  static Future<T?> navigateAndClearStack<T>(Widget page) {
    return Get.offAll<T>(() => GlobalPlayerOverlay(child: page));
  }

  /// عرض bottom sheet مع دعم المشغل
  static Future<T?> showBottomSheetWithPlayer<T>({
    required BuildContext context,
    required Widget child,
    bool isScrollControlled = true,
    bool isDismissible = true,
    bool enableDrag = true,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: isScrollControlled,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      backgroundColor: Colors.transparent,
      builder: (context) => GlobalPlayerOverlay(child: child),
    );
  }

  /// عرض dialog مع دعم المشغل
  static Future<T?> showDialogWithPlayer<T>({
    required BuildContext context,
    required Widget child,
    bool barrierDismissible = true,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => GlobalPlayerOverlay(child: child),
    );
  }
}

/// Extension لتسهيل استخدام التنقل مع المشغل
extension NavigationExtension on Widget {
  /// تحويل أي widget إلى صفحة مع دعم المشغل العالمي
  Widget withGlobalPlayer() {
    return GlobalPlayerOverlay(child: this);
  }

  /// التنقل إلى هذا الـ widget كصفحة مع دعم المشغل
  Future<T?> navigateToWithPlayer<T>() {
    return NavigationHelper.navigateToPage<T>(this);
  }

  /// استبدال الصفحة الحالية بهذا الـ widget مع دعم المشغل
  Future<T?> replaceWithPlayer<T>() {
    return NavigationHelper.navigateAndReplace<T>(this);
  }
}

/// Mixin لإضافة دعم المشغل العالمي للصفحات
mixin GlobalPlayerSupport<T extends StatefulWidget> on State<T> {
  @override
  Widget build(BuildContext context) {
    return GlobalPlayerOverlay(
      child: buildContent(context),
    );
  }

  /// يجب تنفيذ هذه الدالة في الصفحة لبناء المحتوى
  Widget buildContent(BuildContext context);
}

/// Widget مخصص للصفحات التي تحتاج دعم المشغل العالمي
abstract class PlayerAwarePage extends StatelessWidget {
  const PlayerAwarePage({super.key});

  @override
  Widget build(BuildContext context) {
    return GlobalPlayerOverlay(
      child: buildContent(context),
    );
  }

  /// يجب تنفيذ هذه الدالة لبناء محتوى الصفحة
  Widget buildContent(BuildContext context);
}

/// Widget مخصص للصفحات الـ StatefulWidget التي تحتاج دعم المشغل العالمي
abstract class StatefulPlayerAwarePage extends StatefulWidget {
  const StatefulPlayerAwarePage({super.key});

  @override
  State<StatefulPlayerAwarePage> createState() => _StatefulPlayerAwarePageState();

  /// يجب تنفيذ هذه الدالة لبناء محتوى الصفحة
  Widget buildContent(BuildContext context, State state);
}

class _StatefulPlayerAwarePageState extends State<StatefulPlayerAwarePage> {
  @override
  Widget build(BuildContext context) {
    return GlobalPlayerOverlay(
      child: widget.buildContent(context, this),
    );
  }
}
