import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart'; // optional
import 'package:social_media_app/widgets/enhanced_mini_player.dart';
import '../services/simple_audio_player_service.dart';
import '../model/media_item.dart';

class CurrentPlaylistWidget extends StatelessWidget {
  const CurrentPlaylistWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final playerService = SimpleAudioPlayerService.instance;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new_rounded, size: 22),
          onPressed: () => Get.back(),
        ),
      ),
      body: Stack(
        children: [
          // الخلفية: قائمة الملفات
          _buildPlaylistList(),

          // المشغّل المصغَّر العائم فوق القائمة
          Obx(() {
            final current = playerService.currentMediaItem.value;
            final isVisible =
                current != null && playerService.isMinimized.value;

            WidgetsBinding.instance.addPostFrameCallback((_) {
              playerService.setPlayerSize(fullScreen: false);
            });
            return AnimatedPositioned(
              duration: const Duration(milliseconds: 250),
              curve: Curves.easeInOut,
              left: 0,
              right: 0,
              bottom: isVisible ? 0 : -100, // يختفي لأسفل
              child: EnhancedMiniPlayer(),
            );
          }),
        ],
      ),

      // ⬇️ الشريط السفلي الذي يحتوي على الأزرار
      bottomNavigationBar: SafeArea(
        top: false,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface.withOpacity(.95),
            boxShadow: [
              BoxShadow(
                color: Colors.black26,
                blurRadius: 10,
                offset: const Offset(0, -2),
              ),
            ],
            borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // 🔁  Restore original order
              _BottomBarButton(
                icon: Icons.repeat_rounded,
                tooltip: 'الترتيب الأصلي',
                onTap: () => playerService.restoreOriginalOrder(),
              ),

              // 🔀  Shuffle
              _BottomBarButton(
                icon: Icons.shuffle_rounded,
                tooltip: 'تشغيل عشوائي',
                onTap: () => playerService.shufflePlaylist(),
              ),

              // ⏏️  Sort sheet
              _BottomBarButton(
                icon: Icons.repeat_one,
                tooltip: 'ترتيب حسب',
                onTap: () => playerService.repeatMode(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlaylistList() {
    final playerService = SimpleAudioPlayerService.instance;
    return Obx(() {
      final playlist = playerService.currentPlaylist;
      final currentIndex = playerService.currentIndex.value;

      return Column(
        children: [
          _HeaderCard(
              playlist: playlist,
              name: playerService.currentPlaylistName.value),
          Expanded(
            child: playlist.isEmpty
                ? const _EmptyState()
                : ListView.separated(
                    padding: const EdgeInsets.fromLTRB(16, 8, 16, 24),
                    itemCount: playlist.length,
                    separatorBuilder: (_, __) => const SizedBox(height: 12),
                    itemBuilder: (_, index) => _TrackCard(
                      item: playlist[index],
                      index: index,
                      isCurrent: index == currentIndex,
                    ),
                  ),
          ),
        ],
      );
    });
  }

/*
  /* --------------------  WIDGET HELPERS  -------------------- */
  Widget _bottomAction(
    BuildContext context, {
    required IconData icon,
    required VoidCallback onTap,
    Color? color,
    String? tooltip,
  }) =>
      Tooltip(
        message: tooltip ?? '',
        child: IconButton(
          iconSize: 28,
          icon: Icon(icon, color: color),
          onPressed: onTap,
        ),
      );

  void _showSortBottomSheet(BuildContext context) {
    final playerService = SimpleAudioPlayerService.instance;
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      builder: (_) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'ترتيب حسب',
                style: GoogleFonts.tajawal(
                    fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
            ...[
              (Icons.sort_by_alpha, 'الاسم', SortOrder.name),
              (Icons.person, 'الفنان', SortOrder.artist),
              (Icons.album, 'الألبوم', SortOrder.album),
              (Icons.timer, 'المدة', SortOrder.duration),
            ].map(
              (e) => ListTile(
                leading: Icon(e.$1),
                title: Text(e.$2, style: GoogleFonts.tajawal()),
                onTap: () {
                  playerService.sortPlaylist(e.$3);
                  Get.back();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShuffleButton(
          BuildContext context, SimpleAudioPlayerService svc) =>
      Obx(
        () => IconButton(
          tooltip: 'خلط عشوائي',
          icon: Icon(Icons.shuffle,
              color: svc.isShuffleEnabled.value
                  ? Theme.of(context).colorScheme.primary
                  : null),
          onPressed: svc.toggleShuffle,
        ),
      );

  Widget _buildRepeatButton(
          BuildContext context, SimpleAudioPlayerService svc) =>
      Obx(
        () => IconButton(
          tooltip: 'وضع التكرار',
          icon: Icon(_repeatIcon(svc.repeatMode.value),
              color: svc.repeatMode.value != RepeatMode.none
                  ? Theme.of(context).colorScheme.primary
                  : null),
          onPressed: svc.toggleRepeatMode,
        ),
      );

  Widget _buildSortMenu(BuildContext context, SimpleAudioPlayerService svc) =>
      PopupMenuButton<SortOrder>(
        tooltip: 'ترتيب',
        icon: const Icon(Icons.sort_rounded),
        onSelected: svc.sortPlaylist,
        itemBuilder: (_) => [
          _sortItem(Icons.sort_by_alpha, 'الاسم', SortOrder.name),
          _sortItem(Icons.person, 'الفنان', SortOrder.artist),
          _sortItem(Icons.album, 'الألبوم', SortOrder.album),
          _sortItem(Icons.timer, 'المدة', SortOrder.duration),
        ],
      );

  PopupMenuItem<SortOrder> _sortItem(
          IconData icon, String title, SortOrder order) =>
      PopupMenuItem<SortOrder>(
        value: order,
        child: ListTile(
          dense: true,
          contentPadding: EdgeInsets.zero,
          leading: Icon(icon, size: 20),
          title: Text(title, style: GoogleFonts.tajawal()),
        ),
      );

  IconData _repeatIcon(RepeatMode mode) => switch (mode) {
        RepeatMode.none => Icons.repeat,
        RepeatMode.one => Icons.repeat_one,
        RepeatMode.all => Icons.repeat,
      };*/
}

class _BottomBarButton extends StatelessWidget {
  const _BottomBarButton({
    required this.icon,
    required this.tooltip,
    required this.onTap,
  });

  final IconData icon;
  final String tooltip;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(40),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Icon(icon, size: 28),
          ),
        ),
      ),
    );
  }
}

/* --------------------  HEADER CARD  -------------------- */
class _HeaderCard extends StatelessWidget {
  const _HeaderCard({required this.playlist, required this.name});
  final List<MediaItem> playlist;

  final String name;

  @override
  Widget build(BuildContext context) {
    final totalSeconds = playlist
        .where((e) => e.duration != null)
        .fold(0, (sum, e) => sum + e.duration!.inSeconds);
    final totalDuration = _formatDuration(Duration(seconds: totalSeconds));
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + kToolbarHeight + 16,
        left: 24,
        right: 24,
        bottom: 24,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primaryContainer,
            Theme.of(context).colorScheme.primary.withOpacity(.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(32)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            name,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: GoogleFonts.tajawal(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 6),
          Text(
            '${playlist.length} ملف  •  $totalDuration',
            style: GoogleFonts.tajawal(
              fontSize: 14,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }
}

/* --------------------  TRACK CARD  -------------------- */
class _TrackCard extends StatelessWidget {
  const _TrackCard(
      {required this.item, required this.index, required this.isCurrent});
  final MediaItem item;
  final int index;
  final bool isCurrent;

  @override
  Widget build(BuildContext context) {
    final svc = SimpleAudioPlayerService.instance;
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () => svc.safePlayAtIndex(index),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          decoration: BoxDecoration(
            color: isCurrent
                ? Theme.of(context)
                    .colorScheme
                    .primaryContainer
                    .withOpacity(.25)
                : Theme.of(context).cardTheme.color,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black12,
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Leading
              Container(
                width: 46,
                height: 46,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: isCurrent
                      ? Theme.of(context).colorScheme.primary
                      : Colors.grey.withOpacity(.2),
                  shape: BoxShape.circle,
                ),
                child: isCurrent
                    ? Obx(() => Icon(
                          svc.isPlaying.value
                              ? Icons.pause_rounded
                              : Icons.play_arrow_rounded,
                          color: Colors.white,
                        ))
                    : Text(
                        '${index + 1}',
                        style: GoogleFonts.tajawal(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                      ),
              ),
              const SizedBox(width: 14),
              // Texts
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.title ?? 'مجهول',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: GoogleFonts.tajawal(
                        fontWeight:
                            isCurrent ? FontWeight.bold : FontWeight.w600,
                        fontSize: 15,
                      ),
                    ),
                    Text(
                      '${item.artist ?? 'مجهول'}  •  ${_formatDuration(item.duration ?? Duration.zero)}',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: GoogleFonts.tajawal(
                        fontSize: 12,
                        color: Theme.of(context).textTheme.bodySmall?.color,
                      ),
                    ),
                  ],
                ),
              ),
              // Popup menu
              PopupMenuButton(
                icon: const Icon(Icons.more_vert_rounded, size: 22),
                itemBuilder: (_) => [
                  PopupMenuItem(
                    onTap: () => svc.safePlayAtIndex(index),
                    child: ListTile(
                      dense: true,
                      contentPadding: EdgeInsets.zero,
                      leading: const Icon(Icons.play_arrow_rounded, size: 20),
                      title: Text('تشغيل', style: GoogleFonts.tajawal()),
                    ),
                  ),
                  PopupMenuItem(
                    onTap: () => _removeFromPlaylist(index),
                    child: ListTile(
                      dense: true,
                      contentPadding: EdgeInsets.zero,
                      leading: const Icon(Icons.remove_circle_outline_rounded,
                          size: 20),
                      title: Text('إزالة', style: GoogleFonts.tajawal()),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _removeFromPlaylist(int index) {
    final svc = SimpleAudioPlayerService.instance;
    if (index >= 0 && index < svc.currentPlaylist.length) {
      svc.currentPlaylist.removeAt(index);
      if (svc.currentIndex.value >= index) {
        svc.currentIndex.value = (svc.currentIndex.value - 1)
            .clamp(0, svc.currentPlaylist.length - 1);
      }
      Get.snackbar('تم', 'تم إزالة الملف من القائمة');
    }
  }
}

/* --------------------  EMPTY STATE  -------------------- */
class _EmptyState extends StatelessWidget {
  const _EmptyState();
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.queue_music_rounded, size: 72, color: Colors.grey[600]),
          const SizedBox(height: 16),
          Text(
            'لا توجد ملفات في القائمة',
            style: GoogleFonts.tajawal(color: Colors.grey, fontSize: 18),
          ),
        ],
      ),
    );
  }
}

/* --------------------  UTIL  -------------------- */
String _formatDuration(Duration d) {
  final minutes = d.inMinutes.remainder(60).toString().padLeft(2, '0');
  final seconds = d.inSeconds.remainder(60).toString().padLeft(2, '0');
  return '${d.inHours > 0 ? '${d.inHours}:' : ''}$minutes:$seconds';
}
