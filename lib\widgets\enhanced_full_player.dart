import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/simple_audio_player_service.dart';
import '../services/database_service.dart';
import '../model/media_item.dart' as local;
import 'current_playlist_widget.dart';

/// المشغل الكامل المحسن
/// يدعم السحب للتصغير والتنقل بين الملفات بالسحب
class EnhancedFullPlayer extends StatefulWidget {
  const EnhancedFullPlayer({super.key});

  @override
  State<EnhancedFullPlayer> createState() => _EnhancedFullPlayerState();
}

class _EnhancedFullPlayerState extends State<EnhancedFullPlayer>
    with TickerProviderStateMixin {
  late AnimationController _albumArtController;
  late Animation<double> _albumArtAnimation;

  final SimpleAudioPlayerService _playerService =
      SimpleAudioPlayerService.instance;
  final PageController _pageController = PageController();
  bool _isPageChanging = false;
  bool _isPlayingNewTrack = false; // منع التشغيل المتكرر

  @override
  void initState() {
    super.initState();
    print(
        '.initState EnhancedFullPlayepp-----------===========================');
    _albumArtController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    );

    _albumArtAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_albumArtController);

    // بدء دوران صورة الألبوم إذا كان يتم التشغيل
    _playerService.isPlaying.listen((playing) {
      if (mounted) {
        try {
          if (playing) {
            if (!_albumArtController.isAnimating) {
              _albumArtController.repeat();
            }
          } else {
            if (_albumArtController.isAnimating) {
              _albumArtController.stop();
            }
          }
        } catch (e) {
          // تجاهل أخطاء الأنيميشن
          print('خطأ في أنيميشن الألبوم: $e');
        }
      }
    });

    // مزامنة PageView مع الفهرس الحالي مع حماية من الأخطاء
    _playerService.currentIndex.listen((index) {
      if (!_isPageChanging &&
          !_isPlayingNewTrack &&
          _pageController.hasClients) {
        // التأكد من صحة الفهرس قبل التنقل
        final playlist = _playerService.currentPlaylist;
        if (playlist.isNotEmpty && index >= 0 && index < playlist.length) {
          // التحقق من أن الصفحة الحالية مختلفة
          if (_pageController.page?.round() != index) {
            try {
              _pageController.animateToPage(
                index,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            } catch (e) {
              print('خطأ في تحريك PageView: $e');
              // محاولة التنقل المباشر كبديل
              try {
                _pageController.jumpToPage(index);
              } catch (e2) {
                print('فشل في التنقل المباشر: $e2');
              }
            }
          }
        }
      }
    });

    // مراقبة تغييرات القائمة لإعادة بناء PageView
    _playerService.currentPlaylist.listen((playlist) {
      // التأكد من صحة الفهرس الحالي عند تغيير القائمة
      _playerService.validateAndFixCurrentIndex();

      // إعادة بناء الواجهة
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    try {
      // إيقاف الأنيميشن قبل التخلص من الكونترولر
      if (_albumArtController.isAnimating) {
        _albumArtController.stop();
      }
      _albumArtController.dispose();
    } catch (e) {
      // تجاهل الأخطاء في dispose
      print('خطأ في dispose: $e');
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (!_playerService.isPlayerVisible.value ||
          !_playerService.isFullScreen.value ||
          !_playerService.hasCurrentTrack) {
        return const SizedBox.shrink();
      }

      return Scaffold(
        backgroundColor: Colors.black.withValues(alpha: 0.8),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Get.theme.colorScheme.primary.withAlpha(8),
                Get.theme.colorScheme.secondary.withValues(alpha: 0.6),
                Get.theme.scaffoldBackgroundColor,
              ],
            ),
          ),
          child: SafeArea(
            child: GestureDetector(
              onVerticalDragEnd: (details) {
                // السحب للأسفل للتصغير
                if (details.primaryVelocity! > 200) {
                  _playerService.setPlayerSize(fullScreen: false);
                  Navigator.of(context).pop();
                }
              },
              child: Column(
                children: [
                  // شريط التنقل العلوي
                  _buildAppBar(),

                  // المحتوى الرئيسي
                  Expanded(
                    child: PageView.builder(
                      controller: _pageController,
                      itemCount: _playerService.currentPlaylist.isEmpty
                          ? 1
                          : _playerService.currentPlaylist
                              .length, // onPageChanged: (index) {
                      //   if (index != _playerService.currentIndex.value) {
                      //     _isPageChanging = true;
                      //     _playerService.currentIndex.value = index;
                      //     _playerService.playMediaItem(
                      //         _playerService.currentPlaylist[index]);
                      //     // إعادة تعيين العلامة بعد فترة قصيرة
                      //     Future.delayed(const Duration(milliseconds: 500), () {
                      //       _isPageChanging = false;
                      //     });
                      //   }
                      // },

                      onPageChanged: (index) {
                        // منع التشغيل المتكرر والحلقة اللا نهائية
                        if (index != _playerService.currentIndex.value &&
                            !_isPageChanging &&
                            !_isPlayingNewTrack) {
                          _isPageChanging = true;
                          _isPlayingNewTrack = true;

                          print('تغيير الصفحة إلى الفهرس: $index');

                          // استخدام الحارس الآمن للتشغيل
                          _playerService.safePlayAtIndex(index).then((_) {
                            // إعادة تعيين العلامات بعد انتهاء التشغيل
                            Future.delayed(const Duration(milliseconds: 800),
                                () {
                              _isPageChanging = false;
                              _isPlayingNewTrack = false;
                            });
                          }).catchError((error) {
                            print('خطأ في تشغيل المقطع: $error');
                            _isPageChanging = false;
                            _isPlayingNewTrack = false;
                          });
                        }
                      },
                      itemBuilder: (context, index) {
                        // التحقق من وجود القائمة والفهرس
                        final playlist = _playerService.currentPlaylist;
                        if (playlist.isEmpty) {
                          return const Center(
                            child: Text(
                              'لا توجد ملفات حال',
                              style: TextStyle(color: Colors.white),
                            ),
                          );
                        }

                        // التحقق من صحة الفهرس
                        if (index < 0 || index >= playlist.length) {
                          return const Center(
                            child: Text(
                              'خطأ في تحميل الملف',
                              style: TextStyle(color: Colors.red),
                            ),
                          );
                        }

                        return _buildPlayerContent();
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  /// بناء شريط التنقل العلوي
  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          IconButton(
              icon: Icon(
                Icons.keyboard_arrow_down,
                color: Get.theme.colorScheme.onPrimary,
                size: 28,
              ),
              onPressed: () {
                // _playerService.togglePlayerSize();
              }),
          Expanded(
            child: Column(
              children: [
                Obx(() => Text(
                      _playerService.currentPlaylistName.value,
                      style: TextStyle(
                        color: Get.theme.colorScheme.onPrimary
                            .withValues(alpha: 0.8),
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    )),
                const SizedBox(height: 2),
                Text(
                  'تشغيل من قائمة التشغيل',
                  style: TextStyle(
                    color:
                        Get.theme.colorScheme.onPrimary.withValues(alpha: 0.6),
                    fontSize: 10,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          // زر عرض القائمة الحالية
          IconButton(
            icon: Icon(
              Icons.queue_music,
              color: Get.theme.colorScheme.onPrimary,
            ),
            onPressed: () {
              Get.to(() => const CurrentPlaylistWidget());
            },
          ),
          PopupMenuButton<String>(
            icon: Icon(
              Icons.more_vert,
              color: Get.theme.colorScheme.onPrimary,
            ),
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'playlist',
                child: ListTile(
                  leading: Icon(Icons.queue_music),
                  title: Text('عرض القائمة الحالية'),
                ),
              ),
              const PopupMenuItem(
                value: 'add_to_playlist',
                child: ListTile(
                  leading: Icon(Icons.playlist_add),
                  title: Text('إضافة إلى قائمة تشغيل'),
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف الملف'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'hide',
                child: Row(
                  children: [
                    Icon(Icons.visibility_off),
                    SizedBox(width: 8),
                    Text('إخفاء الملف'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit),
                    SizedBox(width: 8),
                    Text('تعديل معلومات الملف'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'audio_settings',
                child: Row(
                  children: [
                    Icon(Icons.equalizer),
                    SizedBox(width: 8),
                    Text('إعدادات الصوت'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء محتوى المشغل
  Widget _buildPlayerContent() {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          // صورة الألبوم
          Expanded(
            flex: 3,
            child: _buildAlbumArt(),
          ),

          const SizedBox(height: 32),

          // معلومات الأغنية
          _buildSongInfo(),

          const SizedBox(height: 24),

          // شريط التقدم
          _buildProgressBar(),

          const SizedBox(height: 32),

          // أزرار التحكم
          _buildControlButtons(),

          const SizedBox(height: 24),

          // أزرار إضافية
          _buildActionButtons(),
        ],
      ),
    );
  }

  /// بناء صورة الألبوم
  Widget _buildAlbumArt() {
    return GestureDetector(
      onHorizontalDragEnd: (details) {
        // السحب الأفقي للتنقل بين الملفات
        if (details.primaryVelocity! > 300) {
          // سحب لليمين - الملف السابق
          _playerService.playPrevious();
        } else if (details.primaryVelocity! < -300) {
          // سحب لليسار - الملف التالي
          _playerService.playNext();
        }
      },
      child: Center(
        child: RotationTransition(
          turns: _albumArtAnimation,
          child: Container(
            width: 280,
            height: 280,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [
                  Get.theme.colorScheme.primary,
                  Get.theme.colorScheme.secondary,
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: Get.theme.colorScheme.primary.withValues(alpha: 0.3),
                  blurRadius: 30,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Icon(
              Icons.music_note,
              size: 120,
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء معلومات الأغنية
  Widget _buildSongInfo() {
    return Column(
      children: [
        Obx(() => Text(
              _playerService.currentTitle,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Get.theme.colorScheme.onPrimary,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            )),
        const SizedBox(height: 8),
        Obx(() => Text(
              _playerService.currentArtist,
              style: TextStyle(
                fontSize: 16,
                color: Get.theme.colorScheme.onPrimary.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
            )),
      ],
    );
  }

  /// بناء شريط التقدم
  Widget _buildProgressBar() {
    return Column(
      children: [
        Obx(() => SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: Get.theme.colorScheme.onPrimary,
                inactiveTrackColor:
                    Get.theme.colorScheme.onPrimary.withValues(alpha: 0.3),
                thumbColor: Get.theme.colorScheme.onPrimary,
                overlayColor:
                    Get.theme.colorScheme.onPrimary.withValues(alpha: 0.2),
              ),
              child: Slider(
                value:
                    _playerService.currentPosition.value.inSeconds.toDouble(),
                max: _playerService.totalDuration.value.inSeconds.toDouble(),
                onChanged: (value) {
                  _playerService.seekTo(Duration(seconds: value.toInt()));
                },
              ),
            )),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Obx(() => Text(
                    _playerService.formattedPosition,
                    style: TextStyle(
                      color: Get.theme.colorScheme.onPrimary
                          .withValues(alpha: 0.8),
                      fontSize: 12,
                    ),
                  )),
              Obx(() => Text(
                    _playerService.formattedDuration,
                    style: TextStyle(
                      color: Get.theme.colorScheme.onPrimary
                          .withValues(alpha: 0.8),
                      fontSize: 12,
                    ),
                  )),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء أزرار التحكم
  Widget _buildControlButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Obx(() => IconButton(
              icon: Icon(
                _playerService.isShuffleEnabled.value
                    ? Icons.shuffle_on_outlined
                    : Icons.shuffle,
                color: _playerService.isShuffleEnabled.value
                    ? Get.theme.colorScheme.onPrimary
                    : Get.theme.colorScheme.onPrimary.withAlpha(6),
              ),
              onPressed: _playerService.toggleShuffle,
            )),
        IconButton(
          icon: Icon(
            Icons.skip_previous,
            color: Get.theme.colorScheme.onPrimary,
            size: 36,
          ),
          onPressed: _playerService.playPrevious,
        ),
        Obx(() => Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Get.theme.colorScheme.onPrimary,
              ),
              child: IconButton(
                icon: Icon(
                  _playerService.isPlaying.value
                      ? Icons.pause
                      : Icons.play_arrow,
                  color: Get.theme.colorScheme.primary,
                  size: 36,
                ),
                onPressed: _playerService.togglePlayPause,
              ),
            )),
        IconButton(
          icon: Icon(
            Icons.skip_next,
            color: Get.theme.colorScheme.onPrimary,
            size: 36,
          ),
          onPressed: _playerService.playNext,
        ),
        Obx(() => IconButton(
              icon: Icon(
                _getRepeatIcon(),
                color: _playerService.repeatMode.value != RepeatMode.none
                    ? Get.theme.colorScheme.onPrimary
                    : Get.theme.colorScheme.onPrimary.withValues(alpha: 0.6),
              ),
              onPressed: _playerService.toggleRepeatMode,
            )),
      ],
    );
  }

  /// بناء الأزرار الإضافية
  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        IconButton(
          icon: Icon(
            Icons.favorite_border,
            color: Get.theme.colorScheme.onPrimary.withValues(alpha: 0.8),
          ),
          onPressed: () {
            // إضافة للمفضلة
          },
        ),
        IconButton(
          icon: Icon(
            Icons.playlist_add,
            color: Get.theme.colorScheme.onPrimary.withValues(alpha: 0.8),
          ),
          onPressed: () {
            _addCurrentTrackToPlaylist();
            // إضافة لقائمة تشغيل
          },
        ),
        IconButton(
          icon: Icon(
            Icons.queue_music,
            color: Get.theme.colorScheme.onPrimary.withValues(alpha: 0.8),
          ),
          onPressed: () {
            // عرض قائمة التشغيل الحالية
            _showCurrentPlaylist();
          },
        ),
        IconButton(
          icon: Icon(
            Icons.share,
            color: Get.theme.colorScheme.onPrimary.withValues(alpha: 0.8),
          ),
          onPressed: () {
            // مشاركة الملف
          },
        ),
      ],
    );
  }

  /// الحصول على أيقونة التكرار
  IconData _getRepeatIcon() {
    switch (_playerService.repeatMode.value) {
      case RepeatMode.none:
        return Icons.repeat;
      case RepeatMode.one:
        return Icons.repeat_one;
      case RepeatMode.all:
        return Icons.repeat_on;
    }
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(String action) {
    switch (action) {
      case 'playlist':
        Get.to(() => const CurrentPlaylistWidget());
        break;
      case 'add_to_playlist':
        _addCurrentTrackToPlaylist();
        break;
      case 'delete':
        _showDeleteConfirmation();
        break;
      case 'hide':
        _showHideConfirmation();
        break;
      case 'edit':
        _showEditDialog();
        break;
      case 'audio_settings':
        _showAudioSettings();
        break;
    }
  }

  /// إضافة المقطع الحالي إلى قائمة تشغيل
  void _addCurrentTrackToPlaylist() {
    final currentTrack = _playerService.currentMediaItem.value;
    if (currentTrack == null) {
      Get.snackbar('خطأ', 'لا يوجد مقطع قيد التشغيل حال');
      return;
    }

    final databaseService = DatabaseService.instance;

    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Get.theme.scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'إضافة "${currentTrack.title}" إلى قائمة التشغيل',
              style: Get.textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // إنشاء قائمة جديدة
            ListTile(
              leading: const Icon(Icons.add),
              title: const Text('إنشاء قائمة جديدة'),
              onTap: () {
                // Get.back();
                _createNewPlaylistWithCurrentTrack(currentTrack);
              },
            ),

            const Divider(),

            // عرض قوائم التشغيل الموجودة
            Obx(() {
              final playlists = databaseService.customPlaylists;
              if (playlists.isEmpty) {
                return const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Text('لا توجد قوائم تشغيل'),
                );
              }

              return ListView.builder(
                shrinkWrap: true,
                itemCount: playlists.length,
                itemBuilder: (context, index) {
                  final playlist = playlists[index];
                  return ListTile(
                    leading: const Icon(Icons.queue_music),
                    title: Text(playlist.name),
                    subtitle: Text('${playlist.itemIds.length} ملف'),
                    onTap: () {
                      Get.back();
                      _addTrackToExistingPlaylist(currentTrack, playlist.id);
                    },
                  );
                },
              );
            }),
          ],
        ),
      ),
    );
  }

  /// إنشاء قائمة تشغيل جديدة مع المقطع الحالي
  void _createNewPlaylistWithCurrentTrack(local.MediaItem track) {
    final TextEditingController nameController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('إنشاء قائمة تشغيل جديدة'),
        content: TextField(
          controller: nameController,
          decoration: const InputDecoration(
            labelText: 'اسم قائمة التشغيل',
            hintText: 'أدخل اسم القائمة',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              final name = nameController.text.trim();
              if (name.isNotEmpty) {
                final databaseService = DatabaseService.instance;
                final success = await databaseService.createPlaylist(
                  name,
                  description: 'قائمة تشغيل مخصصة',
                  type: 'audio',
                );

                if (success) {
                  Get.back();
                  // إضافة الملف إلى القائمة الجديدة
                  final newPlaylist = databaseService.customPlaylists.last;
                  await databaseService.addToPlaylist(
                      newPlaylist.id, track.id ?? '');
                  Get.snackbar('تم', 'تم إنشاء قائمة التشغيل وإضافة المقطع');
                } else {
                  Get.snackbar('خطأ', 'فشل في إنشاء قائمة التشغيل');
                }
              }
            },
            child: const Text('إنشاء'),
          ),
        ],
      ),
    );
  }

  /// إضافة المقطع إلى قائمة ا موجودة
  void _addTrackToExistingPlaylist(
      local.MediaItem track, String playlistId) async {
    final databaseService = DatabaseService.instance;
    final success =
        await databaseService.addToPlaylist(playlistId, track.id ?? '');

    if (success) {
      Get.snackbar('تم', 'تم إضافة المقطع إلى قائمة التشغيل');
    } else {
      Get.snackbar('خطأ', 'فشل في إضافة المقطع إلى قائمة التشغيل');
    }
  }

  /// عرض تأكيد الحذف
  void _showDeleteConfirmation() {
    Get.dialog(
      AlertDialog(
        title: const Text('حذف الملف'),
        content: Text('هل تريد حذف "${_playerService.currentTitle}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              // تنفيذ الحذف
              Get.back();
              Get.snackbar('تم', 'تم حذف الملف بنجاح');
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// عرض تأكيد الإخفاء
  void _showHideConfirmation() {
    Get.dialog(
      AlertDialog(
        title: const Text('إخفاء الملف'),
        content: Text('هل تريد إخفاء "${_playerService.currentTitle}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              // تنفيذ الإخفاء
              Get.back();
              Get.snackbar('تم', 'تم إخفاء الملف بنجاح');
            },
            child: const Text('إخفاء'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار التعديل
  void _showEditDialog() {
    // TODO: تنفيذ حوار تعديل معلومات الملف
    Get.snackbar('قريباً', 'ميزة تعديل معلومات الملف ستكون متاحة قريباً');
  }

  /// عرض إعدادات الصوت
  void _showAudioSettings() {
    // TODO: تنفيذ إعدادات الصوت
    Get.snackbar('قريباً', 'إعدادات الصوت ستكون متاحة قريباً');
  }

  /// عرض قائمة التشغيل الحالية
  void _showCurrentPlaylist() {
    _playerService.togglePlayerSize();
  }
}
