import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:social_media_app/utils/responsive_helper.dart';
import 'package:social_media_app/widgets/enhanced_full_player.dart';
import '../services/simple_audio_player_service.dart';

class EnhancedMiniPlayer extends StatelessWidget {
  final SimpleAudioPlayerService _playerService =
      SimpleAudioPlayerService.instance;

  EnhancedMiniPlayer({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final current = _playerService.currentMediaItem.value;
      if (current == null ||
          !_playerService.isPlayerVisible.value ||
          _playerService.isFullScreen.value) {
        return const SizedBox.shrink();
      }

      return Container(
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).padding.bottom,
        ),
        child: GestureDetector(
          onTap: () {
            _showFullPlayer(context);
          },
          onVerticalDragEnd: (details) {
            // السحب للأعلى لإظهار المشغل الكامل
            if (details.primaryVelocity! < -300) {
              _showFullPlayer(context);
            }
          },
          child: Container(
            height: ResponsiveHelper.miniPlayerHeight(context),
            padding: ResponsiveHelper.horizontalPadding(context),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Get.theme.colorScheme.surface,
                  Get.theme.colorScheme.surface.withValues(alpha: 0.9),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: Get.theme.shadowColor.withValues(alpha: 0.15),
                  blurRadius: 8,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                // صورة الألبوم أو أيقونة افتراضية
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child:
                      current.thumbnail != null && current.thumbnail!.isNotEmpty
                          ? Image.memory(
                              current.thumbnail! as Uint8List,
                              width: ResponsiveHelper.width(context, 55),
                              height: ResponsiveHelper.height(context, 55),
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return _defaultAlbumIcon();
                              },
                            )
                          : _defaultAlbumIcon(),
                ),
                SizedBox(width: ResponsiveHelper.width(context, 12)),
                // معلومات الأغنية مع constraints لتجنب التمدد الزائد
                Expanded(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: ResponsiveHelper.height(context, 55),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          current.title,
                          style: TextStyle(
                            fontSize: ResponsiveHelper.textSize(
                                context, FontSizeType.small),
                            fontWeight: FontWeight.bold,
                            color: Get.theme.colorScheme.onSurface,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: ResponsiveHelper.height(context, 4)),
                        Text(
                          current.artist ?? 'فنان غير معروف',
                          style: TextStyle(
                            fontSize: ResponsiveHelper.textSize(
                                    context, FontSizeType.small) -
                                2,
                            color: Get.theme.colorScheme.onSurface
                                .withValues(alpha: 0.7),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(width: ResponsiveHelper.width(context, 8)),

                // أزرار التحكم
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // زر الأغنية التالية
                    IconButton(
                      icon: Icon(
                        Icons.skip_next,
                        size: ResponsiveHelper.iconSize(context) - 2,
                        color: Get.theme.colorScheme.onSurface
                            .withValues(alpha: 0.8),
                      ),
                      onPressed: () {
                        _playerService.playNext();
                      },
                      splashRadius: 20,
                      tooltip: 'التالي',
                    ),

                    // زر التشغيل / الإيقاف
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Get.theme.colorScheme.primary,
                      ),
                      child: IconButton(
                        icon: Icon(
                          _playerService.isPlaying.value
                              ? Icons.pause
                              : Icons.play_arrow,
                          size: ResponsiveHelper.iconSize(context),
                          color: Get.theme.colorScheme.onPrimary,
                        ),
                        onPressed: () {
                          _playerService.togglePlayPause();
                        },
                        splashRadius: 24,
                        tooltip:
                            _playerService.isPlaying.value ? 'إيقاف' : 'تشغيل',
                      ),
                    ),

                    // زر الإغلاق (إيقاف التشغيل)
                    IconButton(
                      icon: Icon(
                        Icons.close,
                        size: ResponsiveHelper.iconSize(context) - 2,
                        color: Get.theme.colorScheme.onSurface
                            .withValues(alpha: 0.6),
                      ),
                      onPressed: () {
                        _playerService.stop();
                      },
                      splashRadius: 20,
                      tooltip: 'إيقاف التشغيل',
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget _defaultAlbumIcon() {
    return Container(
      width: 55,
      height: 55,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        gradient: LinearGradient(
          colors: [
            Get.theme.colorScheme.primary.withValues(alpha: 0.3),
            Get.theme.colorScheme.secondary.withValues(alpha: 0.3),
          ],
        ),
      ),
      child: Icon(
        Icons.music_note,
        size: 32,
        color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
      ),
    );
  }

  void _showFullPlayer(BuildContext context) {
    // تعيين المشغل إلى وضع الشاشة الكاملة
    _playerService.setPlayerSize(fullScreen: true);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withValues(alpha: 0.5),
      enableDrag: true,
      isDismissible: true,
      builder: (_) => DraggableScrollableSheet(
        initialChildSize: 0.95,
        minChildSize: 0.3,
        maxChildSize: 1.0,
        snap: true,
        snapSizes: const [0.3, 0.95],
        builder: (context, scrollController) {
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Get.theme.colorScheme.primary.withAlpha(8),
                  Get.theme.colorScheme.secondary.withValues(alpha: 0.6),
                  Get.theme.scaffoldBackgroundColor,
                ],
              ),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(20),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: NotificationListener<DraggableScrollableNotification>(
              onNotification: (notification) {
                // انتقال سلس عند السحب للأسفل
                if (notification.extent < 0.4) {
                  Navigator.of(context).pop();
                  _playerService.setPlayerSize(fullScreen: false);
                }
                return true;
              },
              child: Column(
                children: [
                  // مؤشر السحب
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Get.theme.colorScheme.onSurface
                          .withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  // المشغل الكامل
                  const Expanded(child: EnhancedFullPlayer()),
                ],
              ),
            ),
          );
        },
      ),
    ).whenComplete(() {
      // عند إغلاق المشغل الكامل، ارجع للمشغل المصغر
      _playerService.setPlayerSize(fullScreen: false);
    });
  }
}
