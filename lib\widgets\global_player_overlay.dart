import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:social_media_app/utils/responsive_helper.dart';
import 'package:social_media_app/widgets/enhanced_full_player.dart';
import 'package:social_media_app/widgets/enhanced_mini_player.dart';
import '../services/simple_audio_player_service.dart';

/// نظام overlay عالمي لعرض المشغلات في كل أنحاء التطبيق
class GlobalPlayerOverlay extends StatelessWidget {
  final Widget child;
  final SimpleAudioPlayerService _playerService = SimpleAudioPlayerService.instance;

  GlobalPlayerOverlay({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // المحتوى الأساسي للصفحة
          child,
          
          // المشغل المصغر في الأسفل
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Obx(() {
              if (_playerService.currentMediaItem.value == null ||
                  !_playerService.isPlayerVisible.value ||
                  _playerService.isFullScreen.value) {
                return const SizedBox.shrink();
              }
              return EnhancedMiniPlayer();
            }),
          ),
          
          // المشغل الكامل فوق كل شيء
          Positioned.fill(
            child: Obx(() {
              if (_playerService.currentMediaItem.value == null ||
                  !_playerService.isPlayerVisible.value ||
                  !_playerService.isFullScreen.value) {
                return const SizedBox.shrink();
              }
              return Container(
                color: Colors.black.withValues(alpha: 0.8),
                child: const EnhancedFullPlayer(),
              );
            }),
          ),
        ],
      ),
    );
  }
}

/// Widget مساعد لإضافة padding للمحتوى لتجنب تداخل المشغل المصغر
class PlayerAwarePadding extends StatelessWidget {
  final Widget child;
  final SimpleAudioPlayerService _playerService = SimpleAudioPlayerService.instance;

  PlayerAwarePadding({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final hasPlayer = _playerService.currentMediaItem.value != null &&
          _playerService.isPlayerVisible.value &&
          !_playerService.isFullScreen.value;
      
      return Padding(
        padding: EdgeInsets.only(
          bottom: hasPlayer ? ResponsiveHelper.miniPlayerHeight(context) : 0,
        ),
        child: child,
      );
    });
  }
}

/// مدير عالمي للمشغلات
class GlobalPlayerManager {
  static final GlobalPlayerManager _instance = GlobalPlayerManager._internal();
  factory GlobalPlayerManager() => _instance;
  GlobalPlayerManager._internal();

  final SimpleAudioPlayerService _playerService = SimpleAudioPlayerService.instance;

  /// إظهار المشغل المصغر
  void showMiniPlayer() {
    _playerService.showPlayer(fullScreen: false);
    
  }

  /// إظهار المشغل الكامل
  void showFullPlayer() {
    _playerService.showPlayer(fullScreen: true);
  }

  /// إخفاء المشغل
  void hidePlayer() {
    _playerService.hidePlayer();
  }

  /// تبديل حجم المشغل
  void togglePlayerSize() {
    _playerService.togglePlayerSize();
  }

  /// التحقق من وجود مشغل نشط
  bool get hasActivePlayer => 
      _playerService.currentMediaItem.value != null &&
      _playerService.isPlayerVisible.value;

  /// التحقق من وضع الشاشة الكاملة
  bool get isFullScreen => _playerService.isFullScreen.value;

  /// التحقق من وضع المشغل المصغر
  bool get isMiniPlayer => 
      _playerService.isPlayerVisible.value && 
      !_playerService.isFullScreen.value;
}
